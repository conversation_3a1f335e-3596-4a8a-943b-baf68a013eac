using System;
using System.Collections.Generic;
using System.Reflection;
using UnityEditor;
using UnityEngine;

namespace _AddOn.ToolbarExtender.Editor
{
	[InitializeOnLoad]
	public static class ToolbarExtender
	{
		private static GUIStyle m_commandStyle;

		public static readonly List<Action> LeftToolbarGUI = new();
		public static readonly List<Action> RightToolbarGUI = new();

		static ToolbarExtender()
		{
			ToolbarCallback.OnToolbarGUILeft = GUILeft;
			ToolbarCallback.OnToolbarGUIRight = GUIRight;
		}
	
		private static void GUILeft()
		{
			GUILayout.BeginHorizontal();
			foreach (var handler in LeftToolbarGUI)
			{
				handler();
			}

			GUILayout.EndHorizontal();
		}

		private static void GUIRight()
		{
			GUILayout.BeginHorizontal();
			for (var i = RightToolbarGUI.Count - 1; i >= 0; i--)
			{
				RightToolbarGUI[i]();
			}

			GUILayout.EndHorizontal();
		}
	}
}
