// Simple Scroll-Snap - https://assetstore.unity.com/packages/tools/gui/simple-scroll-snap-140884
// Copyright (c) <PERSON>

using UnityEngine;

namespace <PERSON><PERSON><PERSON>.Assets.SimpleScrollSnap
{
    public class RotateX : TransitionEffectBase<RectTransform>
    {
        public override void OnTransition(RectTransform rectTransform, float angle)
        {
            rectTransform.localRotation = Quaternion.Euler(new Vector3(angle, rectTransform.localEulerAngles.y, rectTransform.localEulerAngles.z));
        }
    }
}