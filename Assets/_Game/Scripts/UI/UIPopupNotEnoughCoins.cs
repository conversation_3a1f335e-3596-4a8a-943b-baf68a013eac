using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using OnePuz.Attributes;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Extensions;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI
{
    public class UIPopupNotEnoughCoins : UIBasePanel
    {
        private readonly int _showAnimation = Animator.StringToHash("Popup_TransitionIn");
        private readonly int _hideAnimation = Animator.StringToHash("Popup_TransitionOut");
        
        [Header("Popup Animation")]
        [SerializeField, PreAssigned()]
        private Animator _animator;

        [Header("Elements")]
        [SerializeField, PreAssigned("Container/Popup/ButtonClose")]
        private Button _closeButton;
        [SerializeField, PreAssigned("Container/Popup/ButtonWatch")]
        private Button _watchButton;
        [SerializeField, PreAssigned("Container/Popup/ButtonWatch/Contents/LabelCoin")]
        private TMPro.TMP_Text _coinLabel;
        
        private const int COIN_REWARD = 250;

        protected override void OnBeforeFocus()
        {
            base.OnBeforeFocus();
            
            _coinLabel.text = $"{COIN_REWARD}";
        }

        protected override void OnAfterFocus()
        {
            base.OnAfterFocus();
            
            _closeButton.onClick.AddListener(OnCloseButtonClicked);
            _watchButton.onClick.AddListener(OnWatchButtonClicked);
        }

        protected override void OnBeforeLostFocus()
        {
            base.OnBeforeLostFocus();

            _closeButton.onClick.RemoveListener(OnCloseButtonClicked);
            _watchButton.onClick.RemoveListener(OnWatchButtonClicked);
        }

        private void OnCloseButtonClicked()
        {
            Close();
        }
        
        private void OnWatchButtonClicked()
        {
            if (Application.isEditor)
            {
                ClaimRewards();
                return;
            }
            
            if (Core.Ads.IsRewardedVideoLoaded())
            {
                Core.Ads.SubscribeRewardedVideoWatchedCallback(HandleOnWatchVideoReward);
                Core.Ads.ShowRewardedVideo();
            }
            else
            {
                Core.Ads.RequestRewardedVideo();

                UIShortcut.ShowNoAdsAvailable();
            }
        }
        
        private void HandleOnWatchVideoReward(bool succeed)
        {
            Core.Ads.UnsubscribeRewardedVideoWatchedCallback(HandleOnWatchVideoReward);

            if (!succeed) return;
            
            Core.Analytic.LogEvent($"rv_more_coins", new Firebase.Analytics.Parameter[] {
                new Firebase.Analytics.Parameter("level", DataShortcut.Level.Current + 1)
            });
                
            ClaimRewards();
        }
        
        private void ClaimRewards()
        {
            var rewards = new List<RewardData>()
            {
                new()
                {
                    RewardType = RewardType.CURRENCY,
                    CurrencyType = CurrencyType.COIN,
                    Quantity = COIN_REWARD
                }
            };
            UIShortcut.ShowCongratulation<UIPanelGame>(rewards);
            
            Close();
        }

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(_showAnimation);
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(_hideAnimation);
        }
    }
}