using System.Threading;
using _FeatureHub.Attributes.Core;
using Cysharp.Threading.Tasks;
using OnePuz.Ads;
using OnePuz.Data;
using OnePuz.Extensions;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI
{
    public class UIPopupPause : UIBasePanel
    {
        private readonly int hashFadeIn = Animator.StringToHash("Popup_TransitionIn");

        [SerializeField, ReferenceValue]
        private Animator m_Animator;
        
        [SerializeField, ReferenceValue("Popup/ButtonClose")]
        private Button m_BtnClose;
        
        [SerializeField, ReferenceValue("Popup/ButtonQuit")]
        private Button m_BtnQuit;
        
        [SerializeField, ReferenceValue("Popup/ButtonPrivacy")]
        private Button m_BtnPrivacy;
        
        [SerializeField, ReferenceValue("Popup/LabelVersion")]
        private TMP_Text m_LblVersion;

        [Serial<PERSON>Field, ReferenceValue("PanelMiddle/Music Setting/ButtonMusic")]
        private ToggleButton m_TglMusic;
        
        [Serial<PERSON><PERSON><PERSON>, ReferenceValue("PanelMiddle/Sound Setting/ButtonSound")]
        private ToggleButton m_TglSound;
        
        [SerializeField, ReferenceValue("PanelMiddle/Haptic Setting/ButtonVibration")]
        private ToggleButton m_TglVibration;

        [SerializeField, ReferenceValue(defaultReference: true)]
        private OPBuildSettings m_BuildSetting;
        
        protected override UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            return UniTask.CompletedTask;
        }

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await m_Animator.PlayManual(hashFadeIn);
        }

        protected override void OnBeforeFocus()
        {
            m_LblVersion.text = m_BuildSetting.buildVersion;
            
            m_TglMusic.SetValue(DataShortcut.Setting.MusicEnabled);
            m_TglSound.SetValue(DataShortcut.Setting.SoundEnabled);
            m_TglVibration.SetValue(DataShortcut.Setting.VibrationEnabled);

            m_TglMusic.onValueChanged += HandleMusicChanged;
            m_TglSound.onValueChanged += HandleSoundChanged;
            m_TglVibration.onValueChanged += HandleVibrationChanged;
        }

        private void HandleMusicChanged(bool isOn)
        {
            Core.Audio.SetMusicState(isOn);
        }
        private void HandleSoundChanged(bool isOn)
        {
            Core.Audio.SetSoundState(isOn);
        }
        private void HandleVibrationChanged(bool isOn)
        {
            Core.Vibration.EnableVibration(isOn);
        }

        protected override void OnAfterFocus()
        {
            m_BtnClose.onClick.AddListener(CloseOnClick);
            m_BtnQuit.onClick.AddListener(QuitOnClick);
            m_BtnPrivacy.onClick.AddListener(PrivacyOnClick);
        }

        protected override void OnBeforeLostFocus()
        {
            m_BtnClose.onClick.RemoveListener(CloseOnClick);
            m_BtnQuit.onClick.RemoveListener(QuitOnClick);
            m_BtnPrivacy.onClick.RemoveListener(PrivacyOnClick);
            
            m_TglMusic.onValueChanged -= HandleMusicChanged;
            m_TglSound.onValueChanged -= HandleSoundChanged;
            m_TglVibration.onValueChanged -= HandleVibrationChanged;
        }

        private void CloseOnClick() => Close();

        private void QuitOnClick()
        {
            Core.Ads.ShowInterstitial();
            Close();
            Core.Live.LostLive();
            Core.Home();
        }

        private void PrivacyOnClick()
        {
            NativeScreen.CurrentScreen = NativeScreenTypes.POLICY;
            Application.OpenURL("https://sites.google.com/view/bluefirex-privacy");
        }
    }

}