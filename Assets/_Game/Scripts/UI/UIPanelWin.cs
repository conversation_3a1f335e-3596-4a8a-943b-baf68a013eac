using System.Threading;
using _FeatureHub.Attributes.Core;
using _Main.Scripts.Common;
using Coffee.UIExtensions;
using Cysharp.Threading.Tasks;
using OnePuz.Audio;
using OnePuz.Data;
using OnePuz.Definition;
using Sirenix.OdinInspector;
using OnePuz.Extensions;
using PrimeTween;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI
{
    public class UIPanelWin : UIBasePanel
    {
        private readonly int _hashShow = Animator.StringToHash("PopupWin_Show");
        private readonly int _hashHide = Animator.StringToHash("PopupWin_Hide");

        [SerializeField, ReferenceValue()]
        private Animator _animator;

        [SerializeField, ReferenceValue("Container/ButtonClaimX3")]
        private Button _btnWatchAd;

        [SerializeField, ReferenceValue("Container/ButtonNext")]
        private Button _btnClaim;

        [Serial<PERSON>Field, ReferenceValue("IconCoin/LabelCoin")]
        private TMP_Text m_LblCoin;

        [Ser<PERSON><PERSON><PERSON><PERSON>, ReferenceValue("ButtonClaimX3/GroupIcon"), BoxGroup("ClaimX3")]
        private LayoutElement m_ClaimX3IconLayoutElement;

        [SerializeField]
        private UIParticle[] _confettiParticles;

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(_hashShow);
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(_hashHide);
        }

        protected override void OnBeforeFocus()
        {
            base.OnBeforeFocus();

            var coinEarned = 10;
            
            m_LblCoin.text = coinEarned.ToString();

            DataShortcut.Currency.AddCurrency(CurrencyType.COIN, coinEarned);
        }

        protected override void OnAfterFocus()
        {
            base.OnAfterFocus();
            _btnWatchAd.onClick.AddListener(WatchAdOnClick);
            _btnClaim.onClick.AddListener(ClaimOnClick);

            _btnClaim.interactable = true;
            _btnWatchAd.interactable = true;
        }

        protected override void OnBeforeLostFocus()
        {
            _btnWatchAd.onClick.AddListener(WatchAdOnClick);
            _btnClaim.onClick.AddListener(ClaimOnClick);
        }

        private void WatchAdOnClick()
        {
            if (!Core.Ads.IsRewardedVideoLoaded())
            {
                UIShortcut.ShowNoAdsAvailable();
                return;
            }

            Core.Ads.SubscribeRewardedVideoWatchedCallback(HandleTripleReward);
            Core.Ads.ShowRewardedVideo();
        }

        private void HandleTripleReward(bool succeed)
        {
            Core.Ads.UnsubscribeRewardedVideoWatchedCallback(HandleTripleReward);
            if (!succeed)
            {
                BlockIndicator.Toast("Something went wrong. Please try again later.");
                return;
            }

            _btnClaim.interactable = false;
            _btnWatchAd.interactable = false;

            var coinEarned = 10;
            DataShortcut.Currency.AddCurrency(CurrencyType.COIN, coinEarned * 2);

            Sequence.Create()
                .Group(Tween.Custom(coinEarned, coinEarned * 3, 0.5f, value => { m_LblCoin.text = ((int)value).ToString(); }))
                .OnComplete(this, @this => @this.ClaimOnClick());
        }

        private void ClaimOnClick()
        {
            Core.Next();
        }

        public void OnPlayConfetti(int index)
        {
            _confettiParticles[index].Play();

            AudioShortcut.PlayConfetti();
        }
    }
}