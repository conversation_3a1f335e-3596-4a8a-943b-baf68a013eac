using System.Threading;
using Cysharp.Threading.Tasks;
using OnePuz.Attributes;
using OnePuz.Data;
using OnePuz.Extensions;
using OP.Home;
using UnityEngine;
using UnityEngine.UI;
using Sirenix.OdinInspector;

namespace OnePuz.UI
{
    public class UIPanelHome : UIBasePanel
    {
        private readonly int _showAnimation = Animator.StringToHash("PanelMain_FadeIn");
        private readonly int _hideAnimation = Animator.StringToHash("PanelMain_FadeOut");

        [SerializeField, BoxGroup("General"), PreAssigned()]
        private Animator _animator;

        [SerializeField, BoxGroup("Setting"), PreAssigned("Container/Topbar/Button_Setting")]
        private Button _settingButton;

        [SerializeField, BoxGroup("MiniGames")]
        private RectTransform _itemContainer;
        
        [SerializeField, BoxGroup("MiniGames")]
        private GameObject _itemPrefab;
        
        public override void Init(string id)
        {
            base.Init(id);
            
            SetupMiniGames();
        }
        
        private void SetupMiniGames()
        {
            var modeOrders = DataShortcut.MiniGame.miniGameOrder;
            var definitions = Core.Definition.MiniGame;
            for (var i = 0; i < modeOrders.Count; i++)
            {
                var item = Core.ScenePool.Spawn(_itemPrefab, _itemContainer).GetComponent<UIMiniGameItem>();
                
                var mode = modeOrders[i];
                item.Init(definitions.GetDataByMode(mode), i);
            }
        }

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(_showAnimation);
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(_hideAnimation);
        }

        #region Callbacks

        protected override void OnAfterFocus()
        {
            base.OnAfterFocus();

            _settingButton.onClick.AddListener(HandleSettingOnClicked);
        }

        protected override void OnBeforeLostFocus()
        {
            base.OnBeforeLostFocus();

            _settingButton.onClick.RemoveListener(HandleSettingOnClicked);
        }

        private static void HandleSettingOnClicked()
        {
            UIShortcut.ShowPopup(UIKeys.Panel.SETTINGS);
        }

        #endregion
    }
}