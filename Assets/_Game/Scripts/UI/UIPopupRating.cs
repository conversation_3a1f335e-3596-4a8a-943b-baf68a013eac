using System.Threading;
using OnePuz.Attributes;
using OnePuz.Extensions;
using Cysharp.Threading.Tasks;
using OnePuz.Ads;
using OnePuz.Data;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI
{
    public class UIPopupRating : UIBasePanel
    {
        private readonly int _showAnimation = Animator.StringToHash("Popup_TransitionIn");

        [Header("Popup Animation")]
        [SerializeField, PreAssigned()]
        private Animator _animator;

        [Header("Elements")]
        [SerializeField, PreAssigned("Container/Popup/ButtonRate")]
        private Button _rateButton;

        [SerializeField, PreAssigned("Container/Popup/ButtonClose")]
        private Button _closeButton;

        protected override void OnAfterFocus()
        {
            base.OnAfterFocus();

            _rateButton.onClick.AddListener(OnRateButtonClicked);
            _closeButton.onClick.AddListener(OnNoButtonClicked);
        }

        protected override void OnBeforeLostFocus()
        {
            base.OnBeforeLostFocus();

            _rateButton.onClick.RemoveListener(OnRateButtonClicked);
            _closeButton.onClick.RemoveListener(OnNoButtonClicked);
        }

        private void OnNoButtonClicked()
        {
            Close();
        }

        private void OnRateButtonClicked()
        {
            NativeScreen.CurrentScreen = NativeScreenTypes.RATING;
            Application.OpenURL("market://details?id=" + Application.identifier);

            DataShortcut.Rating.alreadyRated = true;
            Close();
        }

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(_showAnimation).ToUniTask(cancellationToken: cancellationToken);
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await UniTask.CompletedTask;
        }
    }
}