using System;
using Sirenix.OdinInspector;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

namespace _Main.Scripts
{
    public class RenameHelper : MonoBehaviour
    {
    #if UNITY_EDITOR
        public string formatName;
        public Object[] objectsToRename;

        [<PERSON><PERSON>(ButtonSizes.Large), GUIColor("lightblue")]
        private void Rename()
        {
            if(string.IsNullOrEmpty(formatName) || objectsToRename.Length == 0)
                return;
            for (var i = 0; i < objectsToRename.Length; i++)
            {
                var path = AssetDatabase.GetAssetPath(objectsToRename[i]);
                var guid = AssetDatabase.GUIDFromAssetPath(path);
                var newName = string.Format(formatName, $"{i + 1:000}");
                if(objectsToRename[i].name.Contains("hard", StringComparison.OrdinalIgnoreCase))
                    newName += " (Hard)";
                var result = AssetDatabase.RenameAsset(path, newName);

                if (!string.IsNullOrEmpty(result))
                {
                    Debug.Log(result);
                    continue;
                }

                AssetDatabase.SaveAssetIfDirty(guid);
            }

            AssetDatabase.Refresh();
        }
        #endif
    }
}
