using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using UnityEngine;
using OnePuz;
using OnePuz.Services;
using OnePuz.UI;
using UnityEngine.SceneManagement;
#if USING_IAP
using OnePuz.Purchase;
#endif
using OnePuz.Audio;
using OnePuz.Data.Services;
using OnePuz.Live;
using PrimeTween;
using UnityEngine.LowLevel;

public static class Bootstrap
{
    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.AfterAssembliesLoaded)]
    public static void InitUniTaskLoop()
    {
        var loop = PlayerLoop.GetCurrentPlayerLoop();
        PlayerLoopHelper.Initialize(ref loop);
    }
    
    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
    private static void Init()
    {
        // Core services

        ServiceContainer.RegisterService<ITaskService, TaskService>();
        ServiceContainer.RegisterService<ICoroutineService, CoroutineService>();
        ServiceContainer.RegisterService<ICacheService, CacheService>();
        
        if (Application.isEditor && SceneManager.GetActiveScene().name != "Bootstrap")
            LoadAdditionalAsync(enableDelay: false).Forget();
    }

    public static async UniTask LoadAdditionalAsync(bool enableDelay = true)
    {
        // Unity related services

        ServiceContainer.RegisterService<ILogUnityService, LogUnityService>();
        ServiceContainer.RegisterService<IInputService, InputUnityService>();
        ServiceContainer.RegisterService<ITimeService, UnityTimeService>();
        var unityServiceInstance = new GameObject("UnityService").AddComponent<UnityService>();
        Object.DontDestroyOnLoad(unityServiceInstance.gameObject);
        ServiceContainer.RegisterServiceInstance<IUnityService, UnityService>(unityServiceInstance);
        ServiceContainer.RegisterService<GlobalPrefabPoolService>();
        ServiceContainer.RegisterService<ScenePrefabPoolService>();
        var appFocusServiceInstance = new GameObject("AppFocusService").AddComponent<AppFocusService>();
        Object.DontDestroyOnLoad(appFocusServiceInstance.gameObject);
        ServiceContainer.RegisterServiceInstance<AppFocusService, AppFocusService>(appFocusServiceInstance);
        
        ServiceContainer.RegisterService<IVibrationService, VibrationService>();
        await waitAsync(enableDelay);
        
        // Register your custom services here
        
        ServiceContainer.RegisterService<PerformanceService>();
        
        // Definition service
        var definitionServicePrefab = Resources.Load<DefinitionService>("Containers/DefinitionContainer");
        var definitionService = Object.Instantiate(definitionServicePrefab);
        definitionService.gameObject.name = "[Definitions]";
        Object.DontDestroyOnLoad(definitionService.gameObject);
        ServiceContainer.RegisterServiceInstance(definitionService);
        await waitAsync(enableDelay);
        
        // Data service
        var dataServicePrefab = Resources.Load<DataService>("Containers/DataContainer");
        var dataService = Object.Instantiate(dataServicePrefab);
        dataService.gameObject.name = "[Data]";
        Object.DontDestroyOnLoad(dataService.gameObject);
        ServiceContainer.RegisterServiceInstance(dataService);
        await waitAsync(enableDelay);
        
        ServiceContainer.RegisterService<AnalyticService>();
        ServiceContainer.RegisterService<RemoteService>();
        ServiceContainer.RegisterService<NativePluginService>();
        await waitAsync(enableDelay);
        
        //Initialize AudioService
        var audioService = Object.Instantiate(Resources.Load<AudioService>("AudioService"));
        audioService.gameObject.name = "[AudioService]";
        Object.DontDestroyOnLoad(audioService.gameObject);
        ServiceContainer.RegisterServiceInstance(audioService);
        await waitAsync(enableDelay);
        
#if USING_IAP
        var purchaseServiceInstance = new GameObject("PurchaseService").AddComponent<PurchaseService>();
        Object.DontDestroyOnLoad(purchaseServiceInstance.gameObject);
        ServiceContainer.RegisterServiceInstance<PurchaseService, PurchaseService>(purchaseServiceInstance);
        await waitAsync(enableDelay);
#endif
        
        ServiceContainer.RegisterService<LevelChestService>();
        // ServiceContainer.RegisterService<ILiveService, LiveService>();
        ServiceContainer.RegisterService<ILiveService, DummyLiveService>();
        
        // Game state service
        ServiceContainer.RegisterService<IGameStateService, GameStateService>();
        
        // UI service
        ServiceContainer.RegisterService<UIService>();
        await waitAsync(enableDelay);
        
        // Fx service
        ServiceContainer.RegisterService<EffectService>();
        
        // Game flow system
        ServiceContainer.RegisterService<GameFlowService>();
        
        if (Application.isEditor)
            ServiceContainer.RegisterService<IAdService, DummyAdService>();
        else
            ServiceContainer.RegisterService<IAdService, AdService>();
        // ServiceContainer.RegisterService<IAdService, AdService>();
        Core.Ads.SubscribeAdCallbacks(new AdsTracking());
        
        if (SceneManager.GetActiveScene().name == "Home")
        {
            Core.Home();
        }
        else if (SceneManager.GetActiveScene().name == "Game_BlockPuzzle")
        {
            Core.Game(GameMode.BLOCK_PUZZLE);
        }

        OLogger.Log("Bootstrap initialized");
        return;

        async UniTask waitAsync(bool delay)
        {
            if (delay)
                await UniTask.Yield();
        }
    }
    
    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.AfterSceneLoad)]
    private static void AfterSceneLoaded()
    {
        OLogger.Log("Bootstrap change state");
        
        PrimeTweenConfig.warnEndValueEqualsCurrent = false;
    }
}