using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Managers;
using OnePuz.UI;
using UnityEngine;

namespace OnePuz.Controller
{
    public class HomeManager : BaseManager
    {
        public override async UniTask LoadAsync()
        {
            Debug.Log("Home started");

            await UniTask.CompletedTask;
        }

        public override UniTask UnloadAsync()
        {
            return UniTask.CompletedTask;
        }

        public override void Activate()
        {
            AutoShowPopupsAsync().Forget();
        }

        public override UniTask ResetAsync()
        {
            return UniTask.CompletedTask;
        }

        private async UniTask AutoShowPopupsAsync()
        {
            var homePanel = Core.UI.Get<UIPanelHome>();
            if (homePanel)
                await UniTask.WaitUntil(() => homePanel.pState == PanelState.OPENED);

            ShowRating();
        }

        private bool ShowRating()
        {
            if (DataShortcut.Rating.alreadyShownRatingLevels.Contains(DataShortcut.Level.Current))
                return false;

            if (!DataShortcut.Rating.ratingLevelList.Contains(DataShortcut.Level.Current)) return false;
            if (DataShortcut.Rating.alreadyRated || !DataShortcut.Rating.enableRatingPopup)
                return false;

            DataShortcut.Rating.alreadyShownRatingLevels.Add(DataShortcut.Level.Current);
            UIShortcut.EnqueuePopup(UIKeys.Panel.RATING);
            return true;
        }

        private bool ShowMoreLive()
        {
            if (Core.State.PreviousState == GameState.LOSE)
            {
                UIShortcut.EnqueuePopup(UIKeys.Panel.POPUP_MORE_LIVES);
            }
            return true;
        }

        public void Ins_OnBattleClicked()
        {
            Core.Game();
        }
    }
}