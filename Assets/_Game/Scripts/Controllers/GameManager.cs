using System.Diagnostics.CodeAnalysis;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Managers;
using OnePuz.UI;

[SuppressMessage("ReSharper", "CheckNamespace")]
public class GameManager : BaseManager
{
    public override async UniTask LoadAsync()
    {
        await UniTask.Yield();
    }

    public override UniTask UnloadAsync()
    {
        return UniTask.CompletedTask;
    }

    public override async UniTask ResetAsync()
    {
        await UniTask.CompletedTask;
    }

    public override void Activate()
    {
        
    }
}