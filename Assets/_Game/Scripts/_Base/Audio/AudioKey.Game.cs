namespace OnePuz.Audio
{
    public partial class AudioKey
    {
        public const string MusicHome = "Musics/home";
        public const string MusicGameplay = "Musics/in-game";
        
        public const string Typing = "Sounds/typing";
        public const string Victory = "Sounds/win_screen";
        public const string Lose = "Sounds/level_lose";
        public const string Ticktock = "Sounds/Ticktock";
        public const string Timeout = "Sounds/TimeOut";
        public const string Warning = "Sounds/Warning";
        public const string Whoosh = "Sounds/Whoosh";
        public const string CollectItem = "Sounds/Collect item";
        public const string CuteSfx = "Sounds/CuteSfx";
        
        public const string PopupIn = "Sounds/PopIn";
        public const string PopupOut = "Sounds/PopOut";
        
        public const string Confetti = "Sounds/Confetti";

        #region Block Puzzle specific sounds

        public const string ChooseBlockClip = "Sounds/BlockPuzzle/undo";
        public const string DisplayingBlockClip = "Sounds/BlockPuzzle/collect_item_sparkle_pop_10";
        public const string PlacedBlockClip = "Sounds/BlockPuzzle/putBlock";
        public const string PlacedFailedClip = "Sounds/BlockPuzzle/blockwrong";
        public const string BreakBlock_1 = "Sounds/BlockPuzzle/Streak 1";
        public const string BreakBlock_2 = "Sounds/BlockPuzzle/Streak 2";
        public const string BreakBlock_3 = "Sounds/BlockPuzzle/Streak 3";
        public const string BreakBlock_4 = "Sounds/BlockPuzzle/Streak 4";
        public const string BreakBlock_5 = "Sounds/BlockPuzzle/Streak 5";
        public const string BreakBlock_6 = "Sounds/BlockPuzzle/Streak 6";
        public const string BreakBlock_7 = "Sounds/BlockPuzzle/Streak 7";
        public const string BreakBlock_8 = "Sounds/BlockPuzzle/Streak 8";
        public const string BreakBlock_9 = "Sounds/BlockPuzzle/Streak 9";
        public const string BreakBlock_10 = "Sounds/BlockPuzzle/Streak 10";
        public const string HighScoreClip = "Sounds/BlockPuzzle/newRecord";
        public const string LoseClip = "Sounds/BlockPuzzle/over";
        public const string BrokenBlockCollisionClip = "Sounds/BlockPuzzle/particle_collision";
        public const string SpinClip = "Sounds/BlockPuzzle/Spin";
        public const string FireWorkClip = "Sounds/BlockPuzzle/fireworks";
        public const string TickTimerClip = "Sounds/BlockPuzzle/ticking_timer";
        public const string GotSpinClip = "Sounds/BlockPuzzle/spin_get";
        public const string GoodClip = "Sounds/BlockPuzzle/effectGood";
        public const string GreatClip = "Sounds/BlockPuzzle/effectGreat";
        public const string PerfectClip = "Sounds/BlockPuzzle/effectPerfect";
        public const string AmazingClip = "Sounds/BlockPuzzle/effectAmazing";

        #endregion 
    }
}