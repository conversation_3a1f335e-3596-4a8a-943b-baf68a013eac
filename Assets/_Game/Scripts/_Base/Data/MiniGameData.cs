using System.Collections.Generic;

namespace OnePuz.Data
{
    [System.Serializable]
    public class MiniGameData : ISaveData, IVersionedData
    {
        public List<GameMode> miniGameOrder = new List<GameMode>();
        
        public override void SetupDefaultValues()
        {
            miniGameOrder = new List<GameMode>();
            miniGameOrder.AddRange(Core.Definition.MiniGame.defaultMiniGameOrder);
        }

        public int Version { get; set; } = 0;
        
        public void Migrate()
        {
        }

        public void PlayGame(GameMode mode)
        {
            var modeIndex = miniGameOrder.IndexOf(mode);
            if (modeIndex <= 0 || miniGameOrder.Count < 2) return;
            miniGameOrder.Remove(mode);
            miniGameOrder.Insert(1, mode);
        }
    }
}