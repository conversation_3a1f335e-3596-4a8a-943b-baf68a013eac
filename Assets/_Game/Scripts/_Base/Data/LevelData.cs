using Newtonsoft.Json;

namespace OnePuz.Data
{
    [System.Serializable]
    public class LevelData : ISaveData, IVersionedData
    {
        [JsonIgnore]
        public const int TOTAL_LEVEL = 385;

        [JsonIgnore]
        public int Current
        {
            get
            {
                return Core.CurrentMode switch
                {
                    GameMode.TIC_TAC_TOE => Current_TicTacToe,
                    GameMode.BLOCK_PUZZLE => Current_BlockPuzzle,
                    GameMode.ANIMAL_CONNECT => Current_AnimalConnect,
                    _ => 0
                };
            }
        }
        
        [JsonProperty]
        public int Current_TicTacToe { get; private set; }
        [JsonProperty]
        public int Current_BlockPuzzle { get; private set; }
        [JsonProperty]
        public int Current_AnimalConnect { get; private set; }

        [JsonIgnore]
        public int ClampCurrent => Current + 1 > TOTAL_LEVEL ? 100 + ((Current + 1 - TOTAL_LEVEL - 1) % (TOTAL_LEVEL - 100 + 1)) : Current + 1;

        public override void SetupDefaultValues()
        {
            Current_TicTacToe = 0;
            Current_BlockPuzzle = 0;
            Current_AnimalConnect = 0;
        }
        
        public int CompleteLevel(GameMode mode)
        {
            switch (mode)
            {
                case GameMode.TIC_TAC_TOE:
                    Current_TicTacToe++;
                    return Current_TicTacToe;
                case GameMode.BLOCK_PUZZLE:
                    Current_BlockPuzzle++;
                    return Current_BlockPuzzle;
                case GameMode.ANIMAL_CONNECT:
                    Current_AnimalConnect++;
                    return Current_AnimalConnect;
                default:
                    return 0;
            }
        }
        
        public int SetLevel(GameMode mode, int level)
        {
            switch (mode)
            {
                case GameMode.TIC_TAC_TOE:
                    Current_TicTacToe = level;
                    return Current_TicTacToe;
                case GameMode.BLOCK_PUZZLE:
                    Current_BlockPuzzle = level;
                    return Current_BlockPuzzle;
                case GameMode.ANIMAL_CONNECT:
                    Current_AnimalConnect = level;
                    return Current_AnimalConnect;
                default:
                    return level;
            }
        }

        public int Version { get; set; } = 0;
        
        public void Migrate()
        {
            if (Version < 1)
            {
                
            }
        }
    }
}