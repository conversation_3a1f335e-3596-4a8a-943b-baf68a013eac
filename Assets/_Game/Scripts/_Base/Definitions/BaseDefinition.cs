using System.Collections.Generic;
using OnePuz.Data;
using Sirenix.OdinInspector;
using UnityEngine;

namespace OnePuz.Definition
{
    public class BaseDefinition<T, D> : ScriptableObject
        where T : System.Enum
        where D : BaseDefinitionData<T>
    {
        [SerializeField, ListDrawerSettings(DefaultExpandedState = true)]
        private List<D> _definitions = new();

        public List<D> Definitions => _definitions;

        private readonly Dictionary<T, D> _definitionMap = new();

        private void OnEnable()
        {
            _definitionMap.Clear();
            foreach (var definition in _definitions)
            {
                _definitionMap[definition.type] = definition;
            }
        }

        public D GetDefinition(T type)
        {
            return _definitionMap.GetValueOrDefault(type);
        }
    }

    [System.Serializable]
    public class BaseDefinitionData
    {
        [Header("Base")] [PropertyOrder(1)] public string displayName;

        [PropertyOrder(1), PreviewField(64, Alignment = ObjectFieldAlignment.Center)]
        public Sprite icon;

        [PropertyOrder(1)] public Sprite thumbnail;
    }

    [System.Serializable]
    public class BaseDefinitionData<T> : BaseDefinitionData where T : System.Enum
    {
        [Header("General"), PropertyOrder(0)] public T type;
    }
}