using System.Collections.Generic;
using System.Linq;
using OnePuz.Data;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.Serialization;

namespace OnePuz.UI
{
    [CreateAssetMenu(fileName = "UIPrefabDefinitions", menuName = "OnePuz/UIPrefabDefinitions")]
    public class UIPrefabDefinitions : ScriptableObject
    {
        [TableList(AlwaysExpanded = false, DrawScrollView = false, ShowPaging = true, NumberOfItemsPerPage = 5), Space]
        public List<Datum> Global = new List<Datum>();

        [TableList(AlwaysExpanded = false, DrawScrollView = false, ShowPaging = true, NumberOfItemsPerPage = 5), Space]
        public List<Datum> Loading = new List<Datum>();

        [TableList(AlwaysExpanded = false, DrawScrollView = false, ShowPaging = true, NumberOfItemsPerPage = 5), Space]
        public List<Datum> Home = new List<Datum>();

        public List<ModeDatum> Modes = new List<ModeDatum>();

        public List<Datum> GetPanelDataBySceneName(string sceneName)
        {
            switch (sceneName)
            {
                case "Bootstrap":
                    return Loading;
                case "Home":
                    return Home;
                default:
                {
                    foreach (var datum in Modes)
                    {
                        if (string.CompareOrdinal(datum.SceneName, sceneName) == 0)
                            return datum.Data;
                    }

                    return null;
                }
            }
        }

        public ModeDatum GetDefinitionByMode(GameMode mode)
        {
            return Modes.FirstOrDefault(datum => datum.Mode == mode);
        }

        [System.Serializable]
        public class Datum
        {
            [SerializeField, VerticalGroup("Define"), GUIColor("cyan"), PanelKey]
            private string _id = UIKeys.Panel.NONE;

            public string Id => _id;

            [VerticalGroup("Define")]
            public PanelLayer Layer = PanelLayer.UI;

            private int MaxLayerNumber => Layer == PanelLayer.DEFAULT ? (int)(PanelLayer.UI - 2) : 98;

            [PropertyRange(0, "MaxLayerNumber"), SerializeField, VerticalGroup("Define")]
            private int Order = 1;

            public int LayerOrder => (int)Layer + Order;

            [VerticalGroup("Define")]
            public bool AlwaysActive = false;

            public ResourceAsset PrefabReference;
        }

        [System.Serializable]
        public class ModeDatum
        {
            public GameMode Mode = GameMode.CLASSIC;
#if UNITY_EDITOR
            [OnValueChanged("UpdateSceneName")]
            public UnityEditor.SceneAsset Scene;
#endif
            [ReadOnly]
            public string SceneName;
            
            [PanelKey]
            public string StarterPanel = UIKeys.Panel.GAME;
            
            [TableList(AlwaysExpanded = false, DrawScrollView = false, ShowPaging = true, NumberOfItemsPerPage = 5), Space]
            public List<Datum> Data = new List<Datum>();

#if UNITY_EDITOR
            public void UpdateSceneName()
            {
                SceneName = this.Scene.name;
            }
#endif
        }
    }
}