using System.Collections.Generic;
using UnityEngine;

namespace OnePuz.Definition
{
    [CreateAssetMenu(menuName = "OnePuz/Definitions/MiniGameDefinition")]
    public class MiniGameDefinition : ScriptableObject
    {
        public List<GameMode> defaultMiniGameOrder = new List<GameMode>();

        public List<Datum> data;
        
        public Datum GetDataByMode(GameMode mode)
        {
            return data.Find(datum => datum.mode == mode);
        }

        [System.Serializable]
        public class Datum
        {
            public GameMode mode;
            public string name;
            public Sprite icon;
        }
    }
}