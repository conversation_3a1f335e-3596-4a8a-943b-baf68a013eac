using UnityEngine;

namespace OnePuz.Definition
{
    public enum CurrencyType
    {
        COIN,
        GEM,
        MONEY = 100,
        REWARDED_VIDEO = 101,
        
        SPIN_TICKET = 200,
    }
    
    [CreateAssetMenu(menuName = "OnePuz/Definitions/CurrencyDefinition")]
    public class CurrencyDefinition : BaseDefinition<CurrencyType, CurrencyDefinitionData> { }
    
    
    [System.Serializable]
    public class CurrencyDefinitionData : BaseDefinitionData<CurrencyType> { }
}