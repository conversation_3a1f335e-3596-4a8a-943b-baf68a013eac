using System.Collections.Generic;
using AppsFlyerSDK;
using Cysharp.Threading.Tasks;
using OnePuz.Audio;
using OnePuz.Data;
using OnePuz.Managers;
using OnePuz.Services;
using OnePuz.Services.Extensions;
using OnePuz.UI;
using UnityEngine;
using UnityEngine.Rendering.Universal;
using UnityEngine.SceneManagement;

namespace OnePuz
{
    public class GameFlowService : IServiceLoad
    {
        public void Load()
        {
            this.EventSubscribe<UnitySceneLoadedEvent>(OnSceneLoaded);
            this.EventSubscribe<GameStateChangedEvent>(OnGameStateChanged);
        }

        private void OnGameStateChanged(GameStateChangedEvent e)
        {
            switch (e.currentState)
            {
                case GameState.LOADING:
                    break;
                case GameState.HOME:
                    LoadSceneAsync("Home", UIKeys.Panel.HOME, e).Forget();
                    break;
                case GameState.GAME:
                    var uiDefinitions = Core.Definition.UIPrefabs;
                    var datum = uiDefinitions.GetDefinitionByMode(Core.CurrentMode);
                    LoadSceneAsync(datum.SceneName, datum.StarterPanel, e).Forget();
                    
                    DataShortcut.MiniGame.PlayGame(Core.CurrentMode);
                    break;
                case GameState.PLAYING:
                    if (DataShortcut.Level.Current >= 0 && DataShortcut.Level.Current <= 100)
                        Core.Analytic.LogEvent($"start_level_{(DataShortcut.Level.Current + 1):D3}",
                            new Firebase.Analytics.Parameter[] { });
                    break;
                case GameState.REPLAY:
                    ReplayGameAsync(e).Forget();
                    break;
                case GameState.NEXT:
                    NextGameAsync(e).Forget();
                    break;
                case GameState.WIN:
                    WinGameAsync(e).Forget();
                    break;
                case GameState.LOSE:
                    LoseGameAsync(e).Forget();
                    break;
            }

            if (e.previousState is GameState.LOADING)
                Core.Ads.RequestBanner();
        }

        private async UniTaskVoid WinGameAsync(GameStateChangedEvent e)
        {
            await UniTask.Delay(250, cancellationToken: this.GetDestroyCancellationToken());

            AudioShortcut.PlayVictory();
            AudioShortcut.StopMusic();

            await UniTask.Delay(250, cancellationToken: this.GetDestroyCancellationToken());

            var currentLevel = DataShortcut.Level.Current + 1;

            if (currentLevel is > 0 and <= 100)
                Core.Analytic.LogEvent($"win_level_{currentLevel:D3}", new Firebase.Analytics.Parameter[] { });

            if (currentLevel is > 0 and <= 200)
                AppsFlyer.sendEvent($"completed_level_{currentLevel}", new Dictionary<string, string>());

            Core.Ads.ShowInterstitial();

            DataShortcut.Level.CompleteLevel(Core.CurrentMode);

            OLogger.CrashlyticsSetKey("current_level", currentLevel.ToString());

            UIShortcut.ShowPanel(UIKeys.Panel.WIN);
        }

        private async UniTaskVoid LoseGameAsync(GameStateChangedEvent e)
        {
            AudioShortcut.StopMusic();

            // Core.Ads.ShowInterstitial();
            
            UIShortcut.EnqueuePopup(UIKeys.Panel.POPUP_LOSE);
            
            await UniTask.Yield();
        }

        private async UniTaskVoid NextGameAsync(GameStateChangedEvent e)
        {
            await UniTask.Yield();
            await UIShortcut.ShowTransitionAsync();
            Resources.UnloadUnusedAssets().ToUniTask(cancellationToken: this.GetDestroyCancellationToken()).Forget();
            System.GC.Collect();
            await UIShortcut.ClosePanelAsync(UIKeys.Panel.WIN);
            await Core.Manager.ResetAsync();

            Core.Manager.Activate();

            UIShortcut.CloseTransitionAsync().Forget();

            Core.Play();

            AudioShortcut.PlayMusic(GameState.GAME);
        }

        private async UniTaskVoid ReplayGameAsync(GameStateChangedEvent e)
        {
            Core.Ads.ShowInterstitial();
            await UIShortcut.ShowTransitionAsync();
            await Core.Manager.ResetAsync();
            Core.Manager.Activate();

            UIShortcut.CloseTransitionAsync().Forget();

            Core.Play();
        }

        private async UniTaskVoid LoadSceneAsync(string sceneName, string panel, GameStateChangedEvent e)
        {
            await UniTask.DelayFrame(1);
            await UIShortcut.ShowTransitionAsync();

            CleanCameraStack();

            var previousSceneName = SceneManager.GetActiveScene().name;

            OLogger.LogNotice($"scene {sceneName} in state {e.currentState}");
            OLogger.LogNotice($"Unload previous state panels {e.previousState}");

            if (Core.Manager) await Core.Manager.UnloadAsync();

            // Release pool and all spawned objects from pool
            Core.ScenePool.ClearAll(clearSpawned: true);

            // Unload previous state panels
            await UIShortcut.Instance.UnloadPanelsAsync(previousSceneName, this.GetDestroyCancellationToken());

            Core.SetManager(null);

            Resources.UnloadUnusedAssets().ToUniTask(cancellationToken: this.GetDestroyCancellationToken()).Forget();
            Core.Fx.Warmup(e.currentState);

            OLogger.LogNotice($"Loading scene {sceneName}");

            if (previousSceneName != sceneName)
                await SceneManager.LoadSceneAsync(sceneName)
                    .ToUniTask(cancellationToken: this.GetDestroyCancellationToken());

            // Load current state panels
            await UIShortcut.Instance.LoadPanelsAsync(sceneName, this.GetDestroyCancellationToken());
            
            SetupCameraStack();

            var manager = Object.FindAnyObjectByType<BaseManager>();
            if (manager)
            {
                Core.SetManager(manager);
                manager.IsInitialized = false;
                await manager.LoadAsync();
                manager.IsInitialized = true;
            }

            AudioShortcut.PlayMusic(e.currentState);

            await UniTask.Delay(500);

            UIShortcut.ShowPanel(panel);

            if (DataShortcut.Ads.isAdsRemoved || !DataShortcut.Ads.enableAds)
                UIShortcut.ClosePanel(UIKeys.Panel.PANEL_BANNER_ADS);
            else
                UIShortcut.ShowPanel(UIKeys.Panel.PANEL_BANNER_ADS);

            await UIShortcut.CloseTransitionAsync();

            if (manager) manager.Activate();

            if (e.currentState == GameState.GAME) Core.Play();
        }

        private void OnSceneLoaded(UnitySceneLoadedEvent e)
        {
            OLogger.LogNotice($"GameFlowSystem.OnSceneLoaded activeScene {e.sceneName}");
        }

        #region Camera Stack

        private Camera _mainCamera;

        private void SetupCameraStack()
        {
            // Find main camera
            _mainCamera = Camera.main;
            if (!_mainCamera)
                _mainCamera = Object.FindAnyObjectByType<Camera>();
            
            var uiCamera = UIShortcut.Instance.GetUICamera();
            if (!uiCamera)
            {
                OLogger.LogWarning("UI camera not found");
                return;
            }
            
            // Check if there's a main camera in the scene
            if (_mainCamera)
            {
                var cameraData = _mainCamera.GetUniversalAdditionalCameraData();
                if (cameraData)
                {
                    // Configure UI camera for stacking with main camera
                    var uiCameraData = uiCamera.GetUniversalAdditionalCameraData();
                    if (uiCameraData)
                    {
                        // Configure UI camera as overlay camera
                        uiCameraData.renderType = CameraRenderType.Overlay;
                        uiCamera.clearFlags = CameraClearFlags.Nothing;
                        
                        // Check if UI camera is already in stack to avoid duplicates
                        if (cameraData.cameraStack.Contains(uiCamera)) return;
                        
                        // Add to stack
                        cameraData.cameraStack.Add(uiCamera);
                    }
                    else
                    {
                        OLogger.LogWarning("UI camera doesn't have UniversalAdditionalCameraData component");
                    }
                }
                else
                {
                    OLogger.LogWarning("Main camera doesn't have UniversalAdditionalCameraData component");
                }
            }
            else
            {
                // No main camera in the scene - configure UI camera as base camera
                OLogger.LogNotice("No main camera found in scene. Configuring UI camera as base camera.");
                
                var uiCameraData = uiCamera.GetUniversalAdditionalCameraData();
                if (uiCameraData)
                {
                    // Configure UI camera as base camera
                    uiCameraData.renderType = CameraRenderType.Base;
                    
                    // Set appropriate clear flags for a base camera
                    // Usually base cameras clear both color and depth
                    uiCamera.clearFlags = CameraClearFlags.SolidColor;
                    uiCamera.backgroundColor = Color.clear; // Use clear color or any background color
                    
                    // Make sure UI camera can see UI elements
                    // Check if UI layer is included in culling mask
                    if ((uiCamera.cullingMask & (1 << LayerMask.NameToLayer("UI"))) == 0)
                    {
                        OLogger.LogNotice("Adding UI layer to UI camera culling mask");
                        uiCamera.cullingMask |= (1 << LayerMask.NameToLayer("UI"));
                    }
                    
                    // Set UI camera as the main camera for this scene
                    _mainCamera = uiCamera;
                    OLogger.LogNotice("UI camera configured as main camera for this scene");
                }
                else
                {
                    OLogger.LogWarning("UI camera doesn't have UniversalAdditionalCameraData component");
                }
            }
        }

        private void CleanCameraStack()
        {
            if (!_mainCamera) return;
            // Remove UI camera from stack before changing scenes
            var cameraData = _mainCamera.GetUniversalAdditionalCameraData();
            if (cameraData)
            {
                var uiCamera = UIShortcut.Instance.GetUICamera();
                if (uiCamera && cameraData.cameraStack.Contains(uiCamera))
                {
                    cameraData.cameraStack.Remove(uiCamera);
                    OLogger.LogNotice("Removed UI camera from main camera stack");
                }
            }
            _mainCamera = null;
        }

        #endregion
    }
}