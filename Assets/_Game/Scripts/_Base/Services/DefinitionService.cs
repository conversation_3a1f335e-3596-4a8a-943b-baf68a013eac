using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Level;
using OnePuz.Live;
using OnePuz.Shop.Definition;
using OnePuz.UI;
using OP.BlockPuzzle;
using UnityEngine;

namespace OnePuz.Services
{
    public class DefinitionService : MonoBehaviour, IServiceLoad
    {
        [SerializeField] private UIPrefabDefinitions _uiPrefabDefinition;
        public UIPrefabDefinitions UIPrefabs => _uiPrefabDefinition;

        [SerializeField] private GameDefinition _gameDefinition;
        public GameDefinition Game => _gameDefinition;

        [SerializeField] private LevelGroupDefinition _levelGroupDefinition;
        public LevelGroupDefinition LevelGroup => _levelGroupDefinition;

        [SerializeField] private ShopDefinition _shopDefinition;
        public ShopDefinition Shop => _shopDefinition;

        [SerializeField] private CurrencyDefinition _currencyDefinition;
        public CurrencyDefinition Currency => _currencyDefinition;
        
        [SerializeField] private LiveDefinition _liveDefinition;
        public LiveDefinition Live => _liveDefinition;

        [SerializeField] private BoosterDefinition _boosterDefinition;
        public BoosterDefinition Booster => _boosterDefinition;

        [SerializeField] private RewardDefinition _rewardsDefinition;
        public RewardDefinition Rewards => _rewardsDefinition;

        [SerializeField] private LevelChestDefinition _levelChestDefinition;
        public LevelChestDefinition LevelChest => _levelChestDefinition;

        [SerializeField] private TutorialDefinition _tutorialDefinition;
        public TutorialDefinition Tutorial => _tutorialDefinition;
        
        [SerializeField] private MiniGameDefinition _miniGameDefinition;
        public MiniGameDefinition MiniGame => _miniGameDefinition;
        
        [SerializeField] private Definition_BlockPuzzle _blockPuzzleDefinition;
        public Definition_BlockPuzzle BlockPuzzle => _blockPuzzleDefinition;

        public void Load()
        {
            OLogger.Log($"Load");
        }

        public BaseDefinitionData GetRewardDefinition(RewardData rewardData)
        {
            return rewardData.RewardType switch
            {
                RewardType.CURRENCY => Currency.GetDefinition(rewardData.CurrencyType),
                RewardType.BOOSTER => Booster.GetDefinition(rewardData.BoosterType),
#if USE_SKIN_SHOP
                RewardType.Skin => Skin.GetDefinition(rewardData.SkinType, rewardData.SkinId),
#endif
                RewardType.INFINITE_LIVE => Rewards.infiniteLive,
                RewardType.NO_ADS => Rewards.noAds,
                _ => null
            };
        }
    }
}