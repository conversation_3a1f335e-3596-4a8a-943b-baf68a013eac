using System.ComponentModel;
using OnePuz;
using OnePuz.Definition;
using OnePuz.Ads;
using OnePuz.Data;
using OnePuz.Services;
using UnityEngine;
using UnityEngine.SceneManagement;
using static GameManager;

public partial class SROptions
{
    private GameMode _currentMode = GameMode.TIC_TAC_TOE;
    
    [Category("Level Settings")]
    public GameMode CurrentMode
    {
        get { return _currentMode; }
        set { _currentMode = value; }
    }
    
    private int _currentLevel = 1;

    [NumberRange(0, 10000)]
    [Category("Level Settings")]
    public int CurrentLevel
    {
        get { return _currentLevel; }
        set { _currentLevel = value; }
    }

    [Category("Level Settings")]
    public void LoadLevel()
    {
        OLogger.Log("Load level " + _currentLevel);
        DataShortcut.Level.SetLevel(  _currentMode, _currentLevel - 1);
        if (Core.State.CurrentState == GameState.HOME)
            Core.Game();
        else
            Core.Replay();
    }

    private int _moreCoins = 1000;

    [NumberRange(0, 10000)]
    [Category("Coin Settings")]
    public int MoreCoins
    {
        get { return _moreCoins; }
        set { _moreCoins = value; }
    }

    [Category("Coin Settings")]
    public void AddCoins()
    {
        OLogger.Log("Add Coins " + _moreCoins);
        DataShortcut.Currency.AddCurrency(CurrencyType.COIN, _moreCoins);
    }

    private bool _isEnableAds = true;

    [Category("Ads")]
    public bool IsEnableAds
    {
        get => _isEnableAds;
        set
        {
            _isEnableAds = value;

            if (!_isEnableAds)
            {
                Core.Ads.HideBanner();
                DataShortcut.Ads.isAdsRemoved = value;
            }
            else
            {
                DataShortcut.Ads.isAdsRemoved = value;
                Core.Ads.RequestBanner();
            }
        }
    }

    [Category("Ads")]
    [DisplayName("Show Interstitial")]
    public void ShowInterstitial()
    {
        Core.Ads.ShowInterstitial();
    }

    [Category("UI")]
    [DisplayName("Show UI")]
    public void ShowUI()
    {
        Core.UI.ChangeAllOpenedPanelsCanvasGroupAlpha(1);
    }

    [Category("UI")]
    [DisplayName("Hide UI")]
    public void HideUI()
    {
        Core.UI.ChangeAllOpenedPanelsCanvasGroupAlpha(0);
    }

    [Category("Performance")]
    [DisplayName("Test Low Memory")]
    public void TestLowMemory()
    {
        Core.Event.Fire(new UnityAppLowMemoryEvent());
    }
}