using OnePuz.OPTimeline;
using Sirenix.OdinInspector;
using UnityEngine;

namespace OnePuz.UI
{
    public class UICurrency_Gameplay : UICurrency
    {
        [SerializeField, BoxGroup("Show Hide Animations")]
        private OPAnimatorPlayer _showAnimation;
        [SerializeField, BoxGroup("Show Hide Animations")]
        private OPAnimatorPlayer _hideAnimation;
        
        public override void Appear()
        {
            base.Appear();
            _showAnimation.Play();
        }
        
        public override void OnLastRewardReachedTarget()
        {
            _hideAnimation.Play();
        }
    }
}