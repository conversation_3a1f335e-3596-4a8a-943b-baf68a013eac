using System;
using System.Collections.Generic;
using System.Threading;
using _FeatureHub.Attributes.Core;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Extensions;
using OnePuz.Shop.Definition;
using OnePuz.UI;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.Shop.UI
{
    public class UIPanelShop : UIBasePanel, IContainRewardTarget
    {
        private readonly int _showAnimation = Animator.StringToHash("PanelShop_FadeIn");
        private readonly int _hideAnimation = Animator.StringToHash("PanelShop_FadeOut");

        [SerializeField] private ScrollRect _scrollRect;
        [SerializeField, ReferenceValue()] private Animator _animator;

        [Header("Bundles")] [SerializeField] private List<UIShopItem> _bundleItems;

        [SerializeField] private UIShopItem _itemNoAds;

        [SerializeField] private UIShopItem _itemFreeCoin;

        [Header("Gem")] [SerializeField] private RectTransform _gemContainer;
        [SerializeField] private GameObject _gemItemPrefab;
        [SerializeField] private RectTransform _gemTitleRectTransform;
        [SerializeField] private UICurrency _gemCurrency;

        [Header("Coin")] [SerializeField] private RectTransform _coinContainer;
        [SerializeField] private GameObject _coinItemPrefab;
        [SerializeField] private RectTransform _coinTitleRectTransform;
        [SerializeField] private UICurrency _coinCurrency;

        [SerializeField] private Button _closeButton;

        private ShopDefinition _shopDefinition;

        public override void Init(string id)
        {
            base.Init(id);

            _shopDefinition = Core.Definition.Shop;

            SetupBundles();
            // SetupGems();
            SetupCoins();

            _itemNoAds.Init(_shopDefinition.noAdsPack);
            _itemNoAds.onPurchasedSuccessfully += OnItemPurchasedSuccessfully;
            _itemFreeCoin.Init(_shopDefinition.freeCoinPack);
            _itemFreeCoin.onPurchasedSuccessfully += OnItemPurchasedSuccessfully;

            _closeButton.onClick.AddListener(OnClickedButtonClose);
        }

        private void SetupBundles()
        {
            for (var i = 0; i < _shopDefinition.bundles.Count; i++)
            {
                var bundle = _shopDefinition.bundles[i];
                _bundleItems[i].Init(bundle);
                _bundleItems[i].onPurchasedSuccessfully += OnItemPurchasedSuccessfully;
            }
        }

        private void SetupCoins()
        {
            foreach (var item in _shopDefinition.goldPacks)
            {
                var goldItem = Core.GlobalPool.Spawn(_coinItemPrefab, _coinContainer, Vector3.zero, Quaternion.identity)
                    .GetComponent<UIShopItem>();
                goldItem.Init(item);
                goldItem.onPurchasedSuccessfully += OnItemPurchasedSuccessfully;
            }

            // Resize Gold containers
            var goldGridLayout = _coinContainer.GetComponent<GridLayoutGroup>();
            var goldContainerSize = _coinContainer.sizeDelta;
            int goldRows = _shopDefinition.goldPacks.Count % goldGridLayout.constraintCount == 0
                ? _shopDefinition.goldPacks.Count / goldGridLayout.constraintCount
                : _shopDefinition.goldPacks.Count / goldGridLayout.constraintCount + 1;
            _coinContainer.sizeDelta = goldContainerSize;
        }

        private void SetupGems()
        {
            foreach (var item in _shopDefinition.gemPacks)
            {
                var gemItem = Core.GlobalPool.Spawn(_gemItemPrefab, _gemContainer, Vector3.zero, Quaternion.identity)
                    .GetComponent<UIShopItem>();
                gemItem.Init(item);
                gemItem.onPurchasedSuccessfully += OnItemPurchasedSuccessfully;
            }

            // Resize Gem containers
            var gemGridLayout = _gemContainer.GetComponent<GridLayoutGroup>();
            var gemContainerSize = _gemContainer.sizeDelta;
            int gemRows = _shopDefinition.gemPacks.Count % gemGridLayout.constraintCount == 0
                ? _shopDefinition.gemPacks.Count / gemGridLayout.constraintCount
                : _shopDefinition.gemPacks.Count / gemGridLayout.constraintCount + 1;
            gemContainerSize.y = gemGridLayout.cellSize.y * gemRows + gemGridLayout.spacing.y * (gemRows - 1);
            _gemContainer.sizeDelta = gemContainerSize;
        }

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(_showAnimation, 0.5f).ToUniTask(cancellationToken: cancellationToken);
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayReverse(_showAnimation, 0.4f).ToUniTask(cancellationToken: cancellationToken);
        }

        IRewardTarget IContainRewardTarget.GetRewardTarget<T>(T type)
        {
            switch (type)
            {
                case CurrencyType currencyType:
                    return currencyType switch
                    {
                        CurrencyType.COIN => _coinCurrency,
                        CurrencyType.GEM => _gemCurrency,
                        _ => null
                    };
                case BoosterType boosterType:
                    return null;
                default:
                    return null;
            }
        }

        #region Callbacks

        private void OnItemPurchasedSuccessfully(UIShopItem item, ShopItemData data)
        {
            var shopItemsDict = Core.Definition.Shop.GetShopItemDictionary();
            if (!shopItemsDict.TryGetValue(data.productId, out var purchasedItem)) return;
            var listReward = purchasedItem.rewards;

            if (listReward.Count == 1 && listReward[0].RewardType == RewardType.NO_ADS)
            {
                UIShortcut.ShowPopup(UIKeys.Panel.POPUP_ADS_REMOVED);
                DataShortcut.ClaimRewards(new List<RewardData> { listReward[0] });
            }
            else if (data.priceType == CurrencyType.REWARDED_VIDEO)
            {
                // if (Core.State.CurrentState == GameState.HOME)
                //     UIShortcut.ClaimRewards<UIPanelHome>(listReward, item.GetComponent<RectTransform>().position);
                // else
                    UIShortcut.ClaimRewards<UIPanelShop>(listReward, item.GetComponent<RectTransform>().position);
            }
            else
            {
                // if (Core.State.CurrentState == GameState.HOME)
                //     UIShortcut.ShowCongratulation<UIPanelHome>(listReward);
                // else
                    UIShortcut.ShowCongratulation<UIPanelShop>(listReward);
            }
        }

        private void OnClickedButtonClose()
        {
            Close();
        }

        protected override void OnBeforeFocus()
        {
            base.OnBeforeFocus();

            _coinCurrency.Init();
            _coinCurrency.onClick += OnCurrencySelected;
            _coinCurrency.gameObject.SetActive(Core.State.CurrentState != GameState.HOME);

            if (pArgs != null && pArgs.TryGetData("currency", out CurrencyType currencyType))
            {
                if (currencyType == CurrencyType.COIN)
                {
                    StartCoroutine(_scrollRect.FocusOnItemCoroutine(_coinTitleRectTransform, 0.8f, 2f));
                }
                else if (currencyType == CurrencyType.GEM)
                {
                    StartCoroutine(_scrollRect.FocusOnItemCoroutine(_gemTitleRectTransform, 0.8f, 2f));
                }
            }
            else
            {
                StartCoroutine(_scrollRect.FocusAtNormalizedPositionCoroutine(new Vector2(0f, 1f), 2f));
            }
        }

        protected override void OnBeforeLostFocus()
        {
            base.OnBeforeLostFocus();
            pArgs.GetData<Action>("next_popup")?.Invoke();
        }

        protected override void OnAfterLostFocus()
        {
            base.OnAfterLostFocus();
        }

        private void OnCurrencySelected(CurrencyType currencyType)
        {
            if (currencyType == CurrencyType.COIN)
            {
                StartCoroutine(_scrollRect.FocusOnItemCoroutine(_coinTitleRectTransform, 0.8f, 2f));
            }
            else if (currencyType == CurrencyType.GEM)
            {
                StartCoroutine(_scrollRect.FocusOnItemCoroutine(_gemTitleRectTransform, 0.8f, 2f));
            }
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();

            _closeButton.onClick.RemoveListener(OnClickedButtonClose);
        }

        #endregion
    }
}