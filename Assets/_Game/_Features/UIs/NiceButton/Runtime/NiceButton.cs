using OnePuz.Attributes;
using OnePuz.Audio;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using OnePuz.Utils;
using UnityEngine.Serialization;


#if UNITY_EDITOR
using UnityEditor;
#endif

namespace OnePuz.UI
{
    public class NiceButton : Button
    {
        public enum ClickType
        {
            None = 0,
            Animated
        }
        
        public ClickType type;
        public bool listenable;
        public bool enableVibration = true;
        
        [SerializeField, DropDownValueOf(typeof(AudioKey))]
        private string _soundKey = AudioKey.Click;

        private PointerEventData _eventData;
        
        public override void OnPointerClick(PointerEventData eventData)
        {
            switch (type)
            {
                case ClickType.Animated:
                    listenable = true;
                    _eventData = eventData;
                    break;
                default:
                    base.OnPointerClick(eventData);
                    break;
            }

            OnExtendMethod();
        }

        protected virtual void OnExtendMethod()
        {
            Core.Audio.PlaySound(_soundKey, 1f);

            // if (enableVibration)
            //     Core.Vibrate();
        }

        public void ExecuteListener()
        {
            if (!listenable)
                return;

            listenable = false;
            base.OnPointerClick(_eventData);
        }

#if UNITY_EDITOR
        public void RefreshAnimator()
        {
            if (transition != Transition.Animation)
                return;

            var _animator = gameObject.GetComponent<Animator>();
            if (_animator == null)
                _animator = Undo.AddComponent<Animator>(gameObject);

            if (_animator.runtimeAnimatorController != null)
                return;

            _animator.runtimeAnimatorController =
                Resources.Load<RuntimeAnimatorController>("Nice Button (Legacy)");
        }

        public void Rebuild()
        {
            var contentsObject = transform.Find("Contents");
            if (contentsObject != null)
            {
                Debug.Log($"Contents already exists, with parent {contentsObject.parent.name}");
                return;
            }

            var contents = new GameObject("Contents");
            contents.transform.SetParent(transform);
            contents.transform.SetAsFirstSibling();
            Undo.AddComponent<RectTransform>(contents);
            var contentRectTransform = contents.GetComponent<RectTransform>();
            contentRectTransform.localScale = Vector3.one;

            contentRectTransform.anchorMin = Vector2.zero;
            contentRectTransform.anchorMax = Vector2.one;

            contentRectTransform.anchoredPosition = Vector2.zero;
            contentRectTransform.sizeDelta = Vector2.zero;

            for (int i = transform.childCount - 1; i >= 1; i--)
            {
                var child = transform.GetChild(i);
                child.SetParent(contents.transform);
                child.SetAsFirstSibling();
            }

            var image = GetComponent<Image>();
            if (image != null)
            {
                var contentsImage = Undo.AddComponent<Image>(contents);
                EditorUtility.CopySerialized(image, contents.GetComponent<Image>());

                this.targetGraphic = contentsImage;

                Undo.DestroyObjectImmediate(image);
            }
        }

        public void ResizeWithClosestImage()
        {
            var image = GetComponentInChildren<Image>();
            if (image == null)
                return;
            
            Debug.Log($"{name} resize with closest image {image.name}");
            var rectTransform = GetComponent<RectTransform>();
            Undo.RecordObject(rectTransform, "NiceButton Resize");
            rectTransform.sizeDelta = image.sprite.rect.size;
        }

        [MenuItem("CONTEXT/Button/Convert To NiceButton")]
        static void ConvertToNiceButton(MenuCommand command)
        {
            Debug.Log("Convert to NiceButton from Context Menu.");
            var button = command.context as Button;
            var newButton = ComponentConvertTool.Convert<Button, NiceButton>(button);

            if (newButton != null)
            {
                var niceButton = newButton as NiceButton;
                niceButton.RefreshAnimator();
                niceButton.Rebuild();
            }
        }
#endif
    }
}