using System;
using System.Collections;
using _FeatureHub.Attributes.Core;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Extensions;
using OnePuz.Utilities;
using PrimeTween;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI
{
    public class UIRewardItem : MonoBehaviour
    {
        [SerializeField]
        private Image _icon;

        [SerializeField]
        private TMP_Text _labelQuantity;
        
        [SerializeField]
        private ObjectAttractor _attractor;
        
        public ObjectAttractor Attractor => _attractor;

        private RectTransform _rectTransform;
        public RectTransform CachedRectTransform => _rectTransform ??= GetComponent<RectTransform>();

        public RewardData Data { get; private set; }

        public void Setup(RewardData rewardData)
        {
            Data = rewardData;

            switch (rewardData.RewardType)
            {
                case RewardType.CURRENCY when rewardData.CurrencyType is CurrencyType.COIN:
                case RewardType.CURRENCY:
                    _icon.sprite = Core.Definition.Currency.GetDefinition(rewardData.CurrencyType).icon;
                    _labelQuantity.text = $"x{rewardData.Quantity}";
                    break;
                case RewardType.INFINITE_LIVE:
                    _icon.sprite = Core.Definition.Rewards.infiniteLive.icon;
                    var timeSpan = TimeSpan.FromSeconds(rewardData.Quantity);
                    _labelQuantity.text = timeSpan.TotalDays >= 1 ? $"{timeSpan.Days}d" : $"{timeSpan.Hours}h";
                    break;
                case RewardType.BOOSTER:
                    _icon.sprite = Core.Definition.Booster.GetDefinition(rewardData.BoosterType).icon;
                    _labelQuantity.text = $"x{rewardData.Quantity}";
                    break;
                case RewardType.NO_ADS:
                    _icon.sprite = Core.Definition.Rewards.noAds.icon;
                    _labelQuantity.text = string.Empty;
                    break;
                case RewardType.SKIN:
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

            _labelQuantity.rectTransform.localScale = Vector3.zero;
            _icon.SetAlpha(1f);
            _icon.rectTransform.anchoredPosition = Vector2.zero;
        }

        public void PlayIdleAnimation()
        {
            Tween.UIAnchoredPositionY(_icon.rectTransform, 15f, 1f, Ease.Linear, cycles: -1, CycleMode.Yoyo);
        }

        public void PlayAppearAnimation(float delay)
        {
            Tween.Scale(CachedRectTransform, Vector3.zero, Vector3.one, 0.5f, Ease.OutBack, startDelay: delay).OnComplete(() =>
            {
                Tween.Scale(_labelQuantity.rectTransform, Vector3.one, 0.5f, Ease.OutBack);
                
                // Idle animation for the icon
                Tween.UIAnchoredPositionY(_icon.rectTransform, _icon.rectTransform.anchoredPosition.y + 15f, 1f, Ease.Linear, cycles: -1, CycleMode.Yoyo);
            });
        }

        public async UniTask AnimateToTargetAsync(Transform target,  Camera uiCamera)
        {
            var duration = 1f;
            _attractor.Setup(_rectTransform, target);
            Tween.Scale(_labelQuantity.rectTransform, Vector3.zero, duration * 0.3f, Ease.InBack).Forget();
            Tween.Scale(CachedRectTransform, Vector3.one * 0.2f, duration, Ease.InCirc).Forget();
            Tween.Custom(1f, 0.35f,  duration * 0.8f, value => { _icon.SetAlpha(value); }, Ease.InCirc).Forget();
            await _attractor.AnimateToTargetAsync(target, duration, uiCamera);
        }
        
        public void Recycle()
        {
            Tween.StopAll(_icon.rectTransform);
            Tween.StopAll(_labelQuantity.rectTransform);
            Tween.StopAll(CachedRectTransform);
            Core.GlobalPool.Recycle(gameObject);
        }
    }
}