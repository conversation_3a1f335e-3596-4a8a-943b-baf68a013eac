using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using PrimeTween;
using Spine.Unity;
using UnityEngine;

namespace OnePuz.UI
{
    public class UITreasure : MonoBehaviour
    {
        [SerializeField, SpineAnimation]
        private string _treasureIdleAnimation;

        [SerializeField, SpineAnimation]
        private string _treasureOpenAnimation;

        [SerializeField, SpineAnimation]
        private string _treasureOpenedAnimation;
        
        [SerializeField]
        private float _treasureOpenTriggerRate = 0.75f;

        [SerializeField]
        private SkeletonGraphic _treasureSpine;
        
        [SerializeField]
        private AnimationCurve _easingCurve = AnimationCurve.Linear(0f, 0f, 1f, 1f);

        private RectTransform _rectTransform;
        public RectTransform CachedRectTransform => _rectTransform ??= GetComponent<RectTransform>();

        private List<RewardData> _rewards;
        public List<RewardData> Rewards => _rewards;
        
        private RectTransform _originalContainer;
        private Vector2 _originalAnchoredPosition;
        
        public void Setup(List<RewardData> rewards, bool isOpened = false)
        {
            _rewards = rewards;

            if (_treasureSpine)
            {
                _treasureSpine.AnimationState.SetAnimation(0, isOpened ? _treasureOpenedAnimation : _treasureIdleAnimation, true);
            }
        }

        public async UniTask ShowAsync(RectTransform container, Vector2 targetAnchoredPosition)
        {
            _originalContainer = CachedRectTransform.parent as RectTransform;
            _originalAnchoredPosition = CachedRectTransform.anchoredPosition;
            
            const float moveDuration = 0.5f;
            CachedRectTransform.SetParent(container);
            
            _treasureSpine.AnimationState.SetAnimation(0, _treasureIdleAnimation, false);

            var elapsedTime = 0f;
            var originalStartPos = CachedRectTransform.anchoredPosition;

            while (elapsedTime < moveDuration)
            {
                var normalizedTime = elapsedTime / moveDuration;
                var easedTime = _easingCurve.Evaluate(normalizedTime);

                var newPosition = BezierUtils.GetBezierPoint(
                    originalStartPos,
                    targetAnchoredPosition,
                    0.75f,
                    0.7f,
                    false,
                    Vector3.up,
                    easedTime
                );

                CachedRectTransform.anchoredPosition = newPosition;

                elapsedTime += Time.deltaTime;
                await UniTask.Yield();
            }

            CachedRectTransform.anchoredPosition = targetAnchoredPosition;

            _treasureSpine.AnimationState.SetAnimation(0, _treasureOpenAnimation, false);
            await UniTask.Delay(TimeSpan.FromSeconds(_treasureSpine.AnimationState.GetCurrent(0).Animation.Duration * _treasureOpenTriggerRate));
            _treasureSpine.AnimationState.AddAnimation(0, _treasureOpenedAnimation, true, 0f);
        }

        public void ReturnToOriginalContainer()
        {
            if (!_originalContainer) return;
            CachedRectTransform.SetParent(_originalContainer);
                
            if (_originalContainer.gameObject.activeInHierarchy)
            {
                Tween.UIAnchoredPosition(CachedRectTransform, _originalAnchoredPosition, 0.5f, Ease.InOutCubic);
            }
            else
            {
                CachedRectTransform.anchoredPosition = _originalAnchoredPosition;   
            }

            _originalContainer = null;
        }
    }
}