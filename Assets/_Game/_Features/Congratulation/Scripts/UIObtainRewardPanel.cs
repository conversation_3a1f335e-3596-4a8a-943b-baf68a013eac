using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Extensions;
using OnePuz.Utilities;
using PrimeTween;
using UnityEngine;
using UnityEngine.Serialization;

namespace OnePuz.UI
{
    public interface IRewardTarget
    {
        RectTransform GetRewardTargetTransform();
        void OnRewardReachedTarget(int rewardAmount);
        void OnLastRewardReachedTarget();
        void Appear();
    }

    public interface IContainRewardTarget
    {
        IRewardTarget GetRewardTarget<T>(T type) where T : System.Enum;
    }

    public class UIObtainRewardPanel : UIBasePanel
    {
        [Header("Particle Attractors")]
        [SerializeField] private ParticleAttractor _coinParticle;

        [SerializeField] private ParticleAttractor _dynamicParticle;

        [Header("Rewards")]
        [SerializeField] private Transform _rewardContainer;
        [SerializeField] private CurvedGridLayout _rewardLayoutCalculator;

        [SerializeField] private GameObject _rewardItemPrefab;

        private readonly Dictionary<CurrencyType, IRewardTarget> _currencyTargets = new Dictionary<CurrencyType, IRewardTarget>();
        private readonly Dictionary<BoosterType, IRewardTarget> _boosterTargets = new Dictionary<BoosterType, IRewardTarget>();
        private readonly List<UIRewardItem> _spawnedRewardItems = new();

        public override void Init(string id)
        {
            base.Init(id);

            Core.GlobalPool.WarmupCapacity(_rewardItemPrefab, 3);
        }

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await UniTask.CompletedTask;
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await UniTask.CompletedTask;
        }

        public async UniTask ClaimAsync(List<RewardData> rewards, Vector2 position, params IContainRewardTarget[] targetPanels)
        {
            ClearRewardItems();

            for (var i = 0; i < rewards.Count; i++)
            {
                var rewardItem = SpawnRewardItem(rewards[i], _rewardContainer);
                _spawnedRewardItems.Add(rewardItem);

                var offset = _rewardLayoutCalculator.transform.TransformPoint(_rewardLayoutCalculator.GetPositionForIndex(i, rewards.Count));
                var localPosition = rewardItem.CachedRectTransform.InverseTransformPoint(position);
                rewardItem.CachedRectTransform.position = new Vector3(position.x + offset.x, position.y + offset.y, 0f);

                rewardItem.Setup(rewards[i]);
            }
            
            // await UniTask.Delay(TimeSpan.FromSeconds(0.1f * rewards.Count + 1f), cancellationToken: _cancellationToken);
            
            // Claim rewards but don't update UI yet
            DataShortcut.ClaimRewards(rewards, false);

            // Find targets for all rewards
            SetupTargets(rewards, targetPanels);
            
            SetupLayout();

            await ObtainRewardsAsync(true);
        }

        public async UniTask ClaimAsync(List<UIRewardItem> rewardItems, params IContainRewardTarget[] targetPanels)
        {
            ClearRewardItems();
            _spawnedRewardItems.AddRange(rewardItems);
            foreach (var rewardItem in _spawnedRewardItems)
            {
                rewardItem.CachedRectTransform.SetParent(_rewardContainer);
            }

            var rewards = _spawnedRewardItems.Select(item => item.Data).ToList();

            // Claim rewards but don't update UI yet
            DataShortcut.ClaimRewards(rewards, false);

            // Find targets for all rewards
            SetupTargets(rewards, targetPanels);
            
            SetupLayout();

            await ObtainRewardsAsync();
        }

        private async UniTask ObtainRewardsAsync(bool activeOnlyParticles = false)
        {
            var uiCamera = UIShortcut.Instance.GetUICamera();
            var tasks = new List<UniTask>();
            for (var i = 0; i < _spawnedRewardItems.Count; i++)
            {
                var rewardItem = _spawnedRewardItems[i];
                if (!rewardItem)
                    continue;

                var rewardData = rewardItem.Data;

                // Get the target for this reward
                IRewardTarget target = null;
                if (rewardData.RewardType == RewardType.CURRENCY)
                    _currencyTargets.TryGetValue(rewardData.CurrencyType, out target);
                else
                    _boosterTargets.TryGetValue(rewardData.BoosterType, out target);

                if (target == null)
                {
                    Tween.Scale(rewardItem.CachedRectTransform, Vector3.zero, 0.5f, Ease.InBack)
                        .OnComplete(() =>
                        {
                            RecycleRewardItem(rewardItem);
                        }).Forget();
                    continue;
                }

                target.Appear();

                // Determine which method to use based on reward type
                switch (rewardData.RewardType)
                {
                    case RewardType.CURRENCY when rewardData.CurrencyType == CurrencyType.COIN && _coinParticle:
                    {
                        tasks.Add(AnimateCoinToTargetAsync(rewardItem, target, activeOnlyParticles));
                        break;
                    }
                    case RewardType.CURRENCY when rewardData.CurrencyType == CurrencyType.GEM && _dynamicParticle:
                    {
                        tasks.Add(AnimateDynamicToTargetAsync(rewardItem, target, activeOnlyParticles));
                        break;
                    }
                    case RewardType.INFINITE_LIVE:
                    case RewardType.BOOSTER:
                    case RewardType.SKIN:
                    case RewardType.NO_ADS:
                    default:
                    {
                        tasks.Add(AnimateRewardItemToTargetsAsync(rewardItem, target, activeOnlyParticles));
                        break;
                    }
                }

                await UniTask.Delay(200, cancellationToken: _cancellationToken);
            }

            await UniTask.WhenAll(tasks);

            ResetProperties();
            ClearRewardItems();

            async UniTask AnimateRewardItemToTargetsAsync(UIRewardItem rewardItem, IRewardTarget target, bool activeOnlyParticle)
            {
                if (activeOnlyParticle)
                {
                    rewardItem.PlayAppearAnimation(0f);
                    await UniTask.Delay(TimeSpan.FromSeconds(1f));
                }
                await rewardItem.AnimateToTargetAsync(target.GetRewardTargetTransform(), uiCamera);
                target.OnRewardReachedTarget(rewardItem.Data.Quantity);
                target.OnLastRewardReachedTarget();
                rewardItem.Recycle();
            }

            async UniTask AnimateCoinToTargetAsync(UIRewardItem rewardItem, IRewardTarget target, bool activeOnlyParticle)
            {
                if (!activeOnlyParticle)
                    await Tween.Scale(rewardItem.CachedRectTransform, Vector3.zero, 0.25f, Ease.InBack).ToYieldInstruction();
                else
                    rewardItem.CachedRectTransform.localScale = Vector3.zero;

                var quantity = Mathf.Clamp(rewardItem.Data.Quantity, 1, 10);
                _coinParticle.transform.position = rewardItem.CachedRectTransform.position;
                _coinParticle.Play(target.GetRewardTargetTransform(), quantity, uiCamera);
                _coinParticle.OnParticleReachedTarget.AddListener(() => target.OnRewardReachedTarget(rewardItem.Data.Quantity));
                _coinParticle.OnLastParticleReachedTarget.AddListener(() => target.OnLastRewardReachedTarget());
                rewardItem.Recycle();
            }

            async UniTask AnimateDynamicToTargetAsync(UIRewardItem rewardItem, IRewardTarget target, bool activeOnlyParticle)
            {
                if (!activeOnlyParticle)
                    await Tween.Scale(rewardItem.CachedRectTransform, Vector3.zero, 0.25f, Ease.InBack).ToYieldInstruction();
                else
                    rewardItem.CachedRectTransform.localScale = Vector3.zero;

                var quantity = Mathf.Clamp(rewardItem.Data.Quantity, 1, 10);
                _dynamicParticle.transform.position = rewardItem.CachedRectTransform.position;
                _dynamicParticle.Play(target.GetRewardTargetTransform(), quantity, uiCamera);
                _dynamicParticle.OnParticleReachedTarget.AddListener(() => target.OnRewardReachedTarget(rewardItem.Data.Quantity));
                _dynamicParticle.OnLastParticleReachedTarget.AddListener(() => target.OnLastRewardReachedTarget());
                rewardItem.Recycle();
            }
        }

        private void SetupTargets(List<RewardData> rewards, IContainRewardTarget[] targetPanels)
        {
            foreach (var panel in targetPanels)
            {
                if (panel is not IPanel { pIsOpen: true })
                    continue;

                foreach (var rewardData in rewards)
                {
                    var rewardTarget = rewardData.RewardType == RewardType.CURRENCY
                        ? panel.GetRewardTarget(rewardData.CurrencyType)
                        : panel.GetRewardTarget(rewardData.BoosterType);

                    if (rewardTarget == null)
                        continue;

                    if (rewardData.RewardType == RewardType.CURRENCY)
                        _currencyTargets.TryAdd(rewardData.CurrencyType, rewardTarget);
                    else
                        _boosterTargets.TryAdd(rewardData.BoosterType, rewardTarget);
                }
            }
        }

        private void SetupLayout()
        {
            _rewardLayoutCalculator.Setup(1, 5, false, true);
        }

        public UIRewardItem SpawnRewardItem(RewardData rewardData, Transform container)
        {
            var rewardItem = Core.GlobalPool.Spawn(_rewardItemPrefab, container).GetComponent<UIRewardItem>();

            rewardItem.Setup(rewardData);
            return rewardItem;
        }

        private void RecycleRewardItem(UIRewardItem rewardItem)
        {
            Core.GlobalPool.Recycle(rewardItem.gameObject);
        }

        private void ResetProperties()
        {
            _currencyTargets.Clear();
            _boosterTargets.Clear();
        }

        private void ClearRewardItems()
        {
            foreach (var item in _spawnedRewardItems.Where(item => item != null))
            {
                Core.GlobalPool.Recycle(item.gameObject);
            }

            _spawnedRewardItems.Clear();
        }
    }
}