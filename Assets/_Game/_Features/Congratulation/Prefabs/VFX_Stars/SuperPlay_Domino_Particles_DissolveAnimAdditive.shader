Shader "SuperPlay/Domino/Particles/DissolveAnimAdditive" {
	Properties {
		_MainTex ("Texture", 2D) = "white" {}
		[Toggle(ENABLE_OPACITY_SLIDER)] _EnableOpacitySlider ("Enable Opacity Slider", Float) = 0
		_Opacity ("Opacity", Range(-1, 1)) = 1
		_Sharpness ("Sharpness", Range(0, 1)) = 0
		[KeywordEnum(None, Sin)] _UseSin ("Different Start Pos", Float) = 0
		_AnimSpeed ("Animation Speed", Range(-100, 100)) = 0
		[Space] _Stencil ("Stencil ID", Float) = 2
		[Enum(UnityEngine.Rendering.CompareFunction)] _StencilComp ("Stencil Comparison", Float) = 8
		[Enum(UnityEngine.Rendering.StencilOp)] _StencilOp ("Stencil Operation", Float) = 0
		_StencilWriteMask ("Stencil Write Mask", Float) = 255
		_StencilReadMask ("Stencil Read Mask", Float) = 255
		_ColorMask ("Color Mask", Float) = 15
	}
	//DummyShaderTextExporter
	SubShader{
		Tags { "RenderType"="Opaque" }
		LOD 200
		CGPROGRAM
#pragma surface surf Standard
#pragma target 3.0

		sampler2D _MainTex;
		struct Input
		{
			float2 uv_MainTex;
		};

		void surf(Input IN, inout SurfaceOutputStandard o)
		{
			fixed4 c = tex2D(_MainTex, IN.uv_MainTex);
			o.Albedo = c.rgb;
			o.Alpha = c.a;
		}
		ENDCG
	}
}