using OnePuz.Definition;
using OnePuz.Extensions;
using OnePuz.Shop.Definition;
using OnePuz.Shop.UI;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI.Navigation
{
    public class UINavigationPage_Shop : UINavigationPage, IContainRewardTarget
    {
        [SerializeField] private ScrollRect _scrollRect;

        [Header("Gem")] [SerializeField] private RectTransform _gemContainer;
        [SerializeField] private GameObject _gemItemPrefab;
        [SerializeField] private RectTransform _gemTitleRectTransform;
        [SerializeField] private UICurrency _gemCurrency;

        [Header("Coin")] [SerializeField] private RectTransform _coinContainer;
        [SerializeField] private GameObject _coinItemPrefab;
        [SerializeField] private RectTransform _coinTitleRectTransform;
        [SerializeField] private UICurrency _coinCurrency;

        ShopDefinition _shopDefinition;

        public override void Init(string id)
        {
            base.Init(id);

            _shopDefinition = Core.Definition.Shop;

            SetupItems();
        }

        private void SetupItems()
        {
            foreach (var item in _shopDefinition.gemPacks)
            {
                var gemItem = Core.ScenePool.Spawn(_gemItemPrefab, _gemContainer, Vector3.zero, Quaternion.identity)
                    .GetComponent<UIShopItem>();
                gemItem.Init(item);
                gemItem.onPurchasedSuccessfully += OnItemPurchasedSuccessfully;
            }

            foreach (var item in _shopDefinition.goldPacks)
            {
                var goldItem = Core.ScenePool.Spawn(_coinItemPrefab, _coinContainer, Vector3.zero, Quaternion.identity)
                    .GetComponent<UIShopItem>();
                goldItem.Init(item);
                goldItem.onPurchasedSuccessfully += OnItemPurchasedSuccessfully;
            }

            // Resize Gem containers
            var gemGridLayout = _gemContainer.GetComponent<GridLayoutGroup>();
            var gemContainerSize = _gemContainer.sizeDelta;
            int gemRows = _shopDefinition.gemPacks.Count % gemGridLayout.constraintCount == 0
                ? _shopDefinition.gemPacks.Count / gemGridLayout.constraintCount
                : _shopDefinition.gemPacks.Count / gemGridLayout.constraintCount + 1;
            gemContainerSize.y = gemGridLayout.cellSize.y * gemRows + gemGridLayout.spacing.y * (gemRows - 1);
            _gemContainer.sizeDelta = gemContainerSize;

            // Resize Gold containers
            var goldGridLayout = _coinContainer.GetComponent<GridLayoutGroup>();
            var goldContainerSize = _coinContainer.sizeDelta;
            int goldRows = _shopDefinition.goldPacks.Count % goldGridLayout.constraintCount == 0
                ? _shopDefinition.goldPacks.Count / goldGridLayout.constraintCount
                : _shopDefinition.goldPacks.Count / goldGridLayout.constraintCount + 1;
            _coinContainer.sizeDelta = goldContainerSize;
        }

        IRewardTarget IContainRewardTarget.GetRewardTarget<T>(T type)
        {
            switch (type)
            {
                case CurrencyType currencyType:
                    return currencyType switch
                    {
                        CurrencyType.COIN => _coinCurrency,
                        CurrencyType.GEM => _gemCurrency,
                        _ => null
                    };
                case BoosterType boosterType:
                    return null;
                default:
                    return null;
            }
        }

        #region Callbacks

        private void OnItemPurchasedSuccessfully(UIShopItem item, ShopItemData data)
        {
            // TODO: Show popup claimed rewards
            var shopItemsDict = Core.Definition.Shop.GetShopItemDictionary();
            if (!shopItemsDict.TryGetValue(data.productId, out var purchasedItem)) return;
            var listReward = purchasedItem.rewards;
            // UIShortcut.ShowCongratulation<UIPanelHome>(listReward);
        }

        internal override void OnBeforeFocus()
        {
            base.OnBeforeFocus();

            _gemCurrency.Init();
            _coinCurrency.Init();
        }

        internal override void OnAfterFocus()
        {
            base.OnAfterFocus();

            if (pArgs != null && pArgs.TryGetData("currency", out CurrencyType currencyType))
            {
                if (currencyType == CurrencyType.COIN)
                {
                    StartCoroutine(_scrollRect.FocusOnItemCoroutine(_coinTitleRectTransform, 0.8f, 2f));
                }
                else if (currencyType == CurrencyType.GEM)
                {
                    StartCoroutine(_scrollRect.FocusOnItemCoroutine(_gemTitleRectTransform, 0.8f, 2f));
                }
            }
            else
            {
                StartCoroutine(_scrollRect.FocusAtNormalizedPositionCoroutine(new Vector2(0f, 1f), 2f));
            }
        }

        internal override void OnBeforeLostFocus()
        {
            base.OnBeforeLostFocus();
        }

        internal override void OnAfterLostFocus()
        {
            base.OnAfterLostFocus();
        }

        private void OnDestroy()
        {
            Core.ScenePool.Clear(_coinItemPrefab, clearSpawned: true);
            Core.ScenePool.Clear(_gemItemPrefab, clearSpawned: true);
        }

        #endregion
    }
}