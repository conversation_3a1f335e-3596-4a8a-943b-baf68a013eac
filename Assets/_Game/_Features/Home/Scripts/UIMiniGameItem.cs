using OnePuz.Definition;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace OP.Home
{
    public class UIMiniGameItem : MonoBehaviour
    {
        [SerializeField]
        private Button _mainButton;

        [SerializeField]
        private Image _gameIcon;

        [SerializeField]
        private TMP_Text _labelName;

        [SerializeField]
        private Image _backgroundNormalImage;
        [SerializeField]
        private Image _backgroundHighlightImage;

        [SerializeField]
        private Image _playNormalImage;
        [SerializeField]
        private Image _playHighlightImage;

        private MiniGameDefinition.Datum _datum;

        public void Init(MiniGameDefinition.Datum datum, int order)
        {
            _datum = datum;
            
            _gameIcon.sprite = _datum.icon;
            _labelName.text = _datum.name;
            
            if (order != 0)
            {
                _backgroundNormalImage.enabled = true;
                _backgroundHighlightImage.enabled = false;
                _playNormalImage.enabled = true;
                _playHighlightImage.enabled = false;
            }
            else
            {
                _backgroundNormalImage.enabled = false;
                _backgroundHighlightImage.enabled = true;
                _playNormalImage.enabled = false;
                _playHighlightImage.enabled = true;
            }
            
            
            _mainButton.onClick.RemoveAllListeners();
            _mainButton.onClick.AddListener(OnClickedMainButton);
        }

        private void OnClickedMainButton()
        {
            Core.Game(_datum.mode);
        }
    }   
}
