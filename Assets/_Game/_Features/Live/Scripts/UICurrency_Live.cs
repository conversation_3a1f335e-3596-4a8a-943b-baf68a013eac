using System;
using _FeatureHub.Attributes.Core;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Extensions;
using OnePuz.TimeHandler;
using OnePuz.UI;
using Sirenix.OdinInspector;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace OnePuz.Live
{
    public class UICurrency_Live : MonoBehaviour, IRewardTarget
    {
        [SerializeField, ReferenceValue("LabelTimer")]
        private TMP_Text timerText;

        [SerializeField, ReferenceValue("LabelQuantity")]
        private TMP_Text quantityText;

        [SerializeField, ReferenceValue("IconInfinite")]
        private Image _inifiniteIcon;

        [SerializeField, ReferenceValue("Icon")]
        private RectTransform _currencyIconRectTransform;

        private ILiveService _service;

        public void Init()
        {
            _service = Core.Get<ILiveService>();
            this.EventSubscribe<EventLiveChanged>(OnLiveChanged);

            UpdateLiveChange();
        }

        private void OnDestroy()
        {
            this.EventUnsubscribe<EventLiveChanged>(OnLiveChanged);
        }

        private void OnLiveChanged(EventLiveChanged e)
        {
            UpdateLiveChange();
        }

        private void UpdateLiveChange()
        {
            if (_service == null)
                return;

            if (_service.IsInfinite)
            {
                var timeLeftString = TimeFormatter.FormatSmart(_service.TimeLeftToInfiniteLive);
                timerText.text = timeLeftString;

                _inifiniteIcon.enabled = true;
                quantityText.text = string.Empty;
            }
            else if (_service.IsRefilling)
            {
                timerText.text = $@"{_service.TimeLeftToRefill:mm\m\:ss\s}";

                _inifiniteIcon.enabled = false;
                quantityText.text = _service.CurrentLive.ToString();
            }
            else
            {
                timerText.text = "FULL";
                _inifiniteIcon.enabled = false;
                quantityText.text = _service.CurrentLive.ToString();
            }
        }

        public void Ins_OnClick()
        {
            UIShortcut.ShowPopup(UIKeys.Panel.POPUP_MORE_LIVES);
        }

        public RectTransform GetRewardTargetTransform()
        {
            return _currencyIconRectTransform;
        }

        public void OnRewardReachedTarget(int rewardAmount)
        {
        }

        public void OnLastRewardReachedTarget()
        {
        }

        public void Appear()
        {
        }

        [Button(ButtonSizes.Gigantic)]
        private void MinusLive()
        {
            Core.Live.LostLive();
        }
    }
}