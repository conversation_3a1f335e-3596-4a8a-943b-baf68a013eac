using UnityEngine;
using UnityEditor;
using System.Reflection;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using OnePuz.Audio;

namespace OP.BlockPuzzle
{
    public class AudioResourceValidator : EditorWindow
    {
        private Vector2 scrollPosition;
        private List<AudioFileValidationResult> validationResults;

        [MenuItem("Tools/Audio/Find Unused Audio Resources")]
        public static void ShowWindow()
        {
            GetWindow<AudioResourceValidator>("Audio Resource Validator");
        }

        private void OnGUI()
        {
            if (GUILayout.Button("Scan Audio Resources"))
            {
                ScanAudioResources();
            }

            if (validationResults != null)
            {
                EditorGUILayout.Space();
                if (GUILayout.Button("Delete All Unused Clips"))
                {
                    DeleteUnusedClips();
                }
                EditorGUILayout.Space();
                
                scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

                foreach (var result in validationResults)
                {
                    GUI.color = result.IsUsed ? Color.green : Color.red;
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField(result.Path, GUILayout.Width(300));
                    EditorGUILayout.LabelField(result.IsUsed ? "Used" : "Unused");
                    
                    if (GUILayout.Button("Ping", GUILayout.Width(50)))
                    {
                        var asset = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(result.AssetPath);
                        EditorGUIUtility.PingObject(asset);
                    }
                    
                    if (!result.IsUsed)
                    {
                        if (GUILayout.Button("Delete", GUILayout.Width(50)))
                        {
                            DeleteClip(result);
                        }
                    }
                    EditorGUILayout.EndHorizontal();
                }

                GUI.color = Color.white;
                EditorGUILayout.EndScrollView();
            }
        }
        
        private void DeleteClip(AudioFileValidationResult result)
        {
            if (EditorUtility.DisplayDialog("Delete Clip", 
                    $"Are you sure you want to delete {result.Path}?", 
                    "Delete", "Cancel"))
            {
                AssetDatabase.DeleteAsset(result.AssetPath);
                AssetDatabase.Refresh();
                ScanAudioResources();
            }
        }

        private void DeleteUnusedClips()
        {
            if (validationResults == null || !validationResults.Any(r => !r.IsUsed))
                return;

            if (EditorUtility.DisplayDialog("Delete All Unused Clips",
                    "Are you sure you want to delete all unused audio clips?",
                    "Delete All", "Cancel"))
            {
                var unusedClips = validationResults.Where(r => !r.IsUsed).ToList();
                foreach (var clip in unusedClips)
                {
                    AssetDatabase.DeleteAsset(clip.AssetPath);
                }
                AssetDatabase.Refresh();
                ScanAudioResources();
            }
        }

        private void ScanAudioResources()
        {
            validationResults = new List<AudioFileValidationResult>();
            
            // Get all audio paths from AudioKey
            var audioKeyPaths = typeof(AudioKey)
                .GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.DeclaredOnly)
                .Where(f => f.FieldType == typeof(string))
                .Select(f => (string)f.GetValue(null))
                .ToList();

            // Find all audio files in Resources folder
            string[] guids = AssetDatabase.FindAssets("t:AudioClip", new[] { "Assets/_MiniGames/BlockPuzzle/Resources" });
            
            foreach (string guid in guids)
            {
                string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                string resourcePath = GetResourcePath(assetPath);

                validationResults.Add(new AudioFileValidationResult
                {
                    Path = resourcePath,
                    AssetPath = assetPath,
                    IsUsed = audioKeyPaths.Contains(resourcePath)
                });
            }

            validationResults = validationResults.OrderBy(r => r.IsUsed).ToList();
        }

        private string GetResourcePath(string assetPath)
        {
            // Convert full asset path to Resources path
            int resourcesIndex = assetPath.IndexOf("Resources/") + 10;
            string resourcePath = assetPath.Substring(resourcesIndex);
            return Path.ChangeExtension(resourcePath, null); // Remove file extension
        }

        private class AudioFileValidationResult
        {
            public string Path { get; set; }
            public string AssetPath { get; set; }
            public bool IsUsed { get; set; }
        }
    }
}