{"id": 1, "roadmaps": [{"difficulty": "0", "road_map": [{"Start": 1, "End": 30, "Left": "2", "Mid": "2", "Right": "2", "BlockNumber": "1-5,7-0,8-0,9-2,5-0", "Repeat": 1, "Interval_spwan_turn": 2}, {"Start": 31, "End": 40, "Left": "1", "Mid": "1,5", "Right": "1", "Repeat": 1, "BlockNumber": "11-1, 12-1", "Interval_spwan_turn": 3}, {"Start": 41, "End": 50, "Left": "1", "Mid": "1,3", "Right": "1,3", "BlockNumber": "1-4,2-4,10-1,11-1,12-1,13-1,14-1", "Repeat": 1, "Interval_spwan_turn": 3}, {"Start": 51, "End": 60, "Left": "1", "Mid": "1", "Right": "1", "BlockNumber": "1-5,11-1, 12-1", "Repeat": 1, "Interval_spwan_turn": 3}, {"Start": 61, "End": 80, "Left": "1,3", "Mid": "1,3", "Right": "1,3", "BlockNumber": "1-7,2-5,10-1,11-1,12-1,13-1,14-1", "Repeat": 1, "Interval_spwan_turn": 3}, {"Start": 81, "End": 90, "Left": "1", "Mid": "1", "Right": "1", "BlockNumber": "1-3", "Repeat": 0, "Interval_spwan_turn": 3}, {"Start": 91, "End": 110, "Left": "2", "Mid": "2", "Right": "2", "BlockNumber": "1-7,2-5,5-0,7-1,8-1,9-2", "Repeat": 61, "Interval_spwan_turn": 3}, {"Start": 111, "End": 120, "Left": "1", "Mid": "1", "Right": "1", "BlockNumber": "1-3", "Repeat": 61, "Interval_spwan_turn": 3}, {"Start": 121, "End": 130, "Left": "1,4", "Mid": "1,4", "Right": "1,4", "BlockNumber": "1-5,5-0,7-2,8-2,15-0", "Repeat": 61, "Interval_spwan_turn": 3}, {"Start": 131, "End": -1, "Left": "1", "Mid": "1", "Right": "1", "Repeat": 91, "Interval_spwan_turn": 3}]}, {"difficulty": "1", "road_map": [{"Start": 1, "End": 30, "Left": "2", "Mid": "2", "Right": "2", "BlockNumber": "1-5,7-0,8-0,9-2,5-0", "Repeat": 1, "Interval_spwan_turn": 2}, {"Start": 31, "End": 40, "Left": "1", "Mid": "1,5", "Right": "1", "Repeat": 1, "BlockNumber": "11-1, 12-1", "Interval_spwan_turn": 3}, {"Start": 41, "End": 50, "Left": "1", "Mid": "1,3", "Right": "1,3", "BlockNumber": "1-4,2-4,10-1,11-1,12-1,13-1,14-1", "Repeat": 1, "Interval_spwan_turn": 3}, {"Start": 51, "End": 60, "Left": "1", "Mid": "1", "Right": "1", "BlockNumber": "1-5,11-1, 12-1", "Repeat": 1, "Interval_spwan_turn": 3}, {"Start": 61, "End": 80, "Left": "1,3", "Mid": "1,3", "Right": "1,3", "BlockNumber": "1-7,2-5,10-1,11-1,12-1,13-1,14-1", "Repeat": 1, "Interval_spwan_turn": 3}, {"Start": 81, "End": 90, "Left": "1", "Mid": "1", "Right": "1", "BlockNumber": "1-3", "Repeat": 0, "Interval_spwan_turn": 3}, {"Start": 91, "End": 110, "Left": "2", "Mid": "2", "Right": "2", "BlockNumber": "1-7,2-5,5-0,7-1,8-1,9-2", "Repeat": 61, "Interval_spwan_turn": 3}, {"Start": 111, "End": 120, "Left": "1", "Mid": "1", "Right": "1", "BlockNumber": "1-3", "Repeat": 61, "Interval_spwan_turn": 3}, {"Start": 121, "End": 130, "Left": "1,4", "Mid": "1,4", "Right": "1,4", "BlockNumber": "1-5,5-0,7-2,8-2,15-0", "Repeat": 61, "Interval_spwan_turn": 3}, {"Start": 131, "End": -1, "Left": "1", "Mid": "1", "Right": "1", "Repeat": 91, "Interval_spwan_turn": 3}]}, {"difficulty": "2", "road_map": [{"Start": 1, "End": 30, "Left": "2", "Mid": "2", "Right": "2", "BlockNumber": "1-5,7-0,8-0,9-2,5-0", "Repeat": 1, "Interval_spwan_turn": 2}, {"Start": 31, "End": 40, "Left": "1", "Mid": "1,5", "Right": "1", "Repeat": 1, "BlockNumber": "11-1, 12-1", "Interval_spwan_turn": 3}, {"Start": 41, "End": 50, "Left": "1", "Mid": "1,3", "Right": "1,3", "BlockNumber": "1-4,2-4,10-1,11-1,12-1,13-1,14-1", "Repeat": 1, "Interval_spwan_turn": 3}, {"Start": 51, "End": 60, "Left": "1", "Mid": "1", "Right": "1", "BlockNumber": "1-5,11-1, 12-1", "Repeat": 1, "Interval_spwan_turn": 3}, {"Start": 61, "End": 80, "Left": "1,3", "Mid": "1,3", "Right": "1,3", "BlockNumber": "1-7,2-5,10-1,11-1,12-1,13-1,14-1", "Repeat": 1, "Interval_spwan_turn": 3}, {"Start": 81, "End": 90, "Left": "1", "Mid": "1", "Right": "1", "BlockNumber": "1-3", "Repeat": 0, "Interval_spwan_turn": 3}, {"Start": 91, "End": 110, "Left": "2", "Mid": "2", "Right": "2", "BlockNumber": "1-7,2-5,5-0,7-1,8-1,9-2", "Repeat": 61, "Interval_spwan_turn": 3}, {"Start": 111, "End": 120, "Left": "1", "Mid": "1", "Right": "1", "BlockNumber": "1-3", "Repeat": 61, "Interval_spwan_turn": 3}, {"Start": 121, "End": 130, "Left": "1,4", "Mid": "1,4", "Right": "1,4", "BlockNumber": "1-5,5-0,7-2,8-2,15-0", "Repeat": 61, "Interval_spwan_turn": 3}, {"Start": 131, "End": -1, "Left": "1", "Mid": "1", "Right": "1", "Repeat": 91, "Interval_spwan_turn": 3}]}]}