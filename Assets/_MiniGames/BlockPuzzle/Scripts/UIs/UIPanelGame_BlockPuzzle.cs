using System.Threading;
using _FeatureHub.Attributes.Core;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Extensions;
using OnePuz.UI;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace OP.BlockPuzzle
{
    [ReferenceInBackground]
    public class UIPanelGame_BlockPuzzle : UIBasePanel
    { 
        [SerializeField, ReferenceValue] private Animator _animator;

        [SerializeField, ReferenceValue("PanelTop/ButtonSettings")]
        private Button _btnSetting;

        [SerializeField, ReferenceValue("PanelTop/LabelScore")]
        private TMP_Text _labelScore;

        [SerializeField, ReferenceValue("PanelTop/LabelHighScore")]
        private TMP_Text _labelHighScore;


        private readonly int _showAnimation = Animator.StringToHash("PanelGame_Show");

        public override void Init(string id)
        {
            base.Init(id);

            this.EventSubscribe<GameData_BlockPuzzle.OnScoreChangeEvent>(OnScoreChangeEventHandle);

            _labelScore.text = DataShortcut.BlockPuzzle.CurrentScore.ToString();
            _labelHighScore.text = DataShortcut.BlockPuzzle.HighScore.ToString();
        }

        protected override void OnAfterFocus()
        {
            _btnSetting.onClick.AddListener(SettingOnClick);
        }

        protected override void OnBeforeLostFocus()
        {
            _btnSetting.onClick.RemoveListener(SettingOnClick);


            this.EventUnsubscribe<GameData_BlockPuzzle.OnScoreChangeEvent>(OnScoreChangeEventHandle);
        }

        #region ==========[BASE_PANEL]===========

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(_showAnimation, 0.5f).ToUniTask(cancellationToken: cancellationToken);
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayReverse(_showAnimation, 0.5f).ToUniTask(cancellationToken: cancellationToken);
        }

        #endregion

        #region ==========[SCORE]==========

        private void OnScoreChangeEventHandle(GameData_BlockPuzzle.OnScoreChangeEvent obj)
        {
            _labelScore.text = obj.CurrentScore.ToString();
            if (obj.IsNewHighScore)
                _labelHighScore.text = obj.CurrentScore.ToString();
        }

        #endregion

        private void SettingOnClick() => UIShortcut.ShowPopup(UIKeys.Panel.PAUSE);
    }
}