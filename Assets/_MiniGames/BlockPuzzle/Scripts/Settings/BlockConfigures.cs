using System.Collections;
using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;

namespace OP.BlockPuzzle
{
    [CreateAssetMenu(menuName = "Tools/Block Configures")]
    public class BlockConfigures : ScriptableObject
    {
        #region Fields
        [TableList(ShowIndexLabels = true, DrawScrollView = false)]
        public List<BlockData> blocks = new();
        #endregion

        #region Methods
        [PropertyOrder(-1)]
        [<PERSON><PERSON>("Generate Block's Data")]
        public void GenerateBlockData()
        {
            for (var i = 0; i < blocks.Count; i++)
            {
                blocks[i].id = i + 1;
                var block = blocks[i].prefab.GetComponent<Block>();
                block.Configure(i + 1, BlockAngle.Angle0, new List<GridCoordinate>());
                blocks[i].childrenCoordinateByAngleList = new List<ChildrenCoordinateByAngle>();
                BlockAngle[] allAngles = { BlockAngle.Angle0, BlockAngle.Angle90, BlockAngle.Angle180, BlockAngle.Angle270 };
                for (var angleIndex = 0; angleIndex < allAngles.Length; angleIndex++)
                {
                    var blockAngle = allAngles[angleIndex];
                    
                    var childrenCoordinates = GetBlockCoordinatesWithAngles(block.RelativeGridCoordinates, blockAngle);
                    blocks[i].childrenCoordinateByAngleList.Add(new ChildrenCoordinateByAngle {
                        angle = blockAngle,
                        childrenCoordinate = childrenCoordinates
                    });
                }
            }
        }

        private List<GridCoordinate> GetBlockCoordinatesWithAngles(List<GridCoordinate> coordinates, BlockAngle angle)
        {
            var output = new List<GridCoordinate>();
            for (var i = 0; i < coordinates.Count; i++)
            {
                var coord = coordinates[i];
                var outputCoord = angle switch
                {
                    BlockAngle.Angle90 => new GridCoordinate(coord.y, -1 * coord.x),
                    BlockAngle.Angle180 => new GridCoordinate(coord.x * -1, coord.y * -1),
                    BlockAngle.Angle270 => new GridCoordinate(coord.y * -1, coord.x),
                    _ => coord
                };
                output.Add(outputCoord);
            }

            return output;
        }

        public BlockData GetBlockById(int id)
        {
            for (var i = 0; i < blocks.Count; i++)
            {
                if (blocks[i].id == id)
                    return blocks[i];
            }

            return blocks.Count > 0 ? blocks[0] : null;
        }
        #endregion
    }

    [System.Serializable]
    public class BlockData
    {
        [VerticalGroup("Properties")]
        [TableColumnWidth(30)]
        public int id;

        [VerticalGroup("Properties")]
        public bool differentAngleEachTurn = false;

        [VerticalGroup("Properties")]
        public List<BlockAngle> availableAngles;

        [VerticalGroup("Properties")]
        public List<ChildrenCoordinateByAngle> childrenCoordinateByAngleList;
        
        [AssetsOnly]
        [PropertyOrder(-1)]
        [TableColumnWidth(50, false)]
        [PreviewField(Alignment = ObjectFieldAlignment.Center, Height = 50)]
        public GameObject prefab;
    }

    [System.Serializable]
    public class ChildrenCoordinateByAngle
    {
        public BlockAngle angle;
        public List<GridCoordinate> childrenCoordinate;
    }

    public enum BlockDifficulty
    {
        EASY, MEDIUM, HARD, INTERMEDIATE
    }

    [System.Serializable]
    public class BlockGroup
    {
        public int id;
        public List<int> childrenIdArray;
    }

    [System.Serializable]
    public class RulesByDifficulty
    {
        public int difficulty;
        public List<SpawnByTurnRule> rules = new();
    }

    [System.Serializable]
    public class SpawnByTurnRule
    {
        public int startTurn;
        public int endTurn;
        public string leftGroupIds;
        public string midGroupIds;
        public string rightGroupIds;
        public List<int> leftBlockIds;
        public List<int> midBlockIds;
        public List<int> rightBlockIds;
        public List<SpawnCountLimitById> spawnCountLimitByIdArray;
        public int repeatOnTurn;
        public int spawnCountForFit;

        public int GetSpawnLimitById(int blockId)
        {
            for (var i = 0; i < spawnCountLimitByIdArray.Count; i++)
                if (spawnCountLimitByIdArray[i].id == blockId)
                    return spawnCountLimitByIdArray[i].spawnCountLimit;

            return -1;
        }
    }

    [System.Serializable]
    public struct SpawnCountLimitById
    {
        public int id;
        public int spawnCountLimit;
    }
}
