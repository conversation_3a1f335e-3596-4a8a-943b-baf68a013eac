using System.Collections.Generic;
using UnityEngine;

namespace OP.BlockPuzzle
{
    [CreateAssetMenu(menuName = "Tools/Grid Element Type Configures")]
    public class GridElementTypeConfigures : ScriptableObject
    {
        #region Fields
        [SerializeField]
        private List<GridElementTypeData> m_Configures;
        #endregion

        #region Methods
        public GridElementTypeData GetDataByType(GridElementType type)
        {
            for (int i = 0; i < m_Configures.Count; i++)
            {
                if (m_Configures[i].type == type)
                    return m_Configures[i];
            }

            return m_Configures[1];
        }

        public GridElementTypeData GetRandomData()
        {
            int randomIndex = Random.Range(1, 8);
            return GetDataByType((GridElementType)randomIndex);
        }

        public int GetBlockTypeCount()
        {
            return m_Configures.Count - 1; // Cause of we have a gray block here
        }
        #endregion
    }

    [System.Serializable]
    public class GridElementTypeData
    {
        public GridElementType type;
        public Sprite icon;
        public Sprite iconWithStar;
        public Sprite iconHighlight;
        public Sprite iconWithStarHighlight;
    }

    public enum GridElementType
    {
        Gray = 0,
        Blue = 1,
        Yellow = 2,
        Red = 3,
        Pink = 4,
        <PERSON> = 5, 
        <PERSON>an = 6,
        Purple = 7,
        Orange = 8,
    }
}
