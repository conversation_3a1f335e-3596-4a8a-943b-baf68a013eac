using System.Collections;
using UnityEngine;

namespace OP.BlockPuzzle
{
    public class AutoDespawn : MonoBehaviour
    {
        [SerializeField]
        private float m_DelayTime = 4f;

        private void OnEnable()
        {
            StartCoroutine(YieldDespawn());
        }

        private IEnumerator YieldDespawn()
        {
            yield return new WaitForSeconds(m_DelayTime);
            Core.ScenePool.Recycle(transform.gameObject);
        }
    }
}
