using OnePuz.Audio;
using OnePuz.Data;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.Serialization;

namespace OP.BlockPuzzle
{
    public class ScoreController : BaseBehaviour
    {
        #region Fields

        [SerializeField] 
        private Camera _camera;

        [FormerlySerializedAs("mNewBestAnimator")]
        [FormerlySerializedAs("m_NewBestAnimator")] [Header("Best Score")] 
        [SerializeField]
        private Animator _newBestAnimator;

        [FormerlySerializedAs("mScorePrefab")]
        [FormerlySerializedAs("m_ScorePrefab")] [Header("Prefabs")] 
        [SerializeField]
        private GameObject _scorePrefab;
     

        #endregion

        #region Properties

        private bool _hasShownNewBestScore;

        public int CurrentScore => DataShortcut.BlockPuzzle.CurrentScore;
        public int HighestScore => DataShortcut.BlockPuzzle.HighScore;
        public int CurrentCombo => DataShortcut.BlockPuzzle.CurrentCombo;

        private int _continuousPlacedUnMatchableBlockNumber;

        #endregion

        #region Methods

        public void LoadSavedData()
        {
            if (CurrentScore == HighestScore)
                _hasShownNewBestScore = true;
        }

        public void Reset()
        {
            DataShortcut.BlockPuzzle.SetScore(0);
            DataShortcut.BlockPuzzle.SetCombo(0);

            _hasShownNewBestScore = false;
        }

        public void HandleLose()
        {
            DataShortcut.BlockPuzzle.SetScore(0);
        }

        [Button(SdfIconType.Bucket)]
        public void AddScore(int bonusScore)
        {
            DataShortcut.BlockPuzzle.AddScore(bonusScore);
            if (CurrentScore != HighestScore) return;
            if (_hasShownNewBestScore || HighestScore <= 0 || !DataShortcut.BlockPuzzle.ShownTutorial) return;
            _hasShownNewBestScore = true;

            _newBestAnimator.Play("new_best_show");
            AudioShortcut.PlayHighScore();
        }

        private void AddScoreText(int currentCombo, int brokenRowColCount, Vector3 position, GridElementType type)
        {
            var bonusScore = GetScoreByBrokenRowColCount(brokenRowColCount);
            bonusScore *= (int)((CurrentCombo > 2) ? CurrentCombo * 0.5f : 1f);
            DataShortcut.BlockPuzzle.AddScore(bonusScore);

            var floatingScore = Core.ScenePool.Spawn(_scorePrefab, position, default).GetComponent<FloatingScore>();

            var screenWidthInUnit = _camera.orthographicSize * 2f * _camera.aspect;
            floatingScore.VTransform.position =
                new Vector3(Mathf.Clamp(position.x, -screenWidthInUnit / 2f + 2.5f, screenWidthInUnit / 2f - 2.5f),
                    position.y, position.z);
            floatingScore.Show(currentCombo, brokenRowColCount, bonusScore, type);
        }

        public void HandlePlacedMatchableBlock(int brokenRowColCount, Vector3 position, GridElementType type)
        {
            DataShortcut.BlockPuzzle.AddCombo(1);

            AddScoreText(CurrentCombo, brokenRowColCount, position, type);

            _continuousPlacedUnMatchableBlockNumber = 0;
        }

        public void HandlePlacedUnMatchableBlock()
        {
            _continuousPlacedUnMatchableBlockNumber++;
            if (_continuousPlacedUnMatchableBlockNumber < 3) return;
            _continuousPlacedUnMatchableBlockNumber = 0;
            DataShortcut.BlockPuzzle.SetCombo(0);
        }

        private static int GetScoreByBrokenRowColCount(int brokenRowColCount)
        {
            return brokenRowColCount switch
            {
                0 => 0,
                1 => 100,
                2 => 300,
                3 => 600,
                4 => 1000,
                5 => 1500,
                _ => 400 * brokenRowColCount
            };
        }
        #endregion
    }
}