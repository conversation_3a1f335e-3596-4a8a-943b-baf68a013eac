using OnePuz.Audio;
using Sirenix.OdinInspector;
using UnityEngine;
using PrimeTween;

namespace OP.BlockPuzzle
{
    public class FloatingScore : BaseBehaviour
    {
        [SerializeField]
        private Transform m_ScoreTextTransform;

        [SerializeField]
        private TMPro.TextMeshPro m_ScoreText;

        [Header("Combo")]
        [SerializeField]
        private Transform m_ComboNumberContainerTransform;

        [SerializeField]
        private TMPro.TextMeshPro m_ComboNumberText;

        [SerializeField]
        private Transform m_ComboTextTransform;

        [SerializeField]
        private ParticleSystem m_ComboNumberParticle;

        [Header("Animation")]
        [SerializeField] AnimationCurve m_TextScaleInCurve;

        [SerializeField] AnimationCurve m_TextScaleOutCurve;

        [Header("Highlight Text")]
        [SerializeField] SpriteRenderer m_HighlightTextRenderer;

        [SerializeField] Transform m_HighlightTextTransform;
        [SerializeField] ParticleSystem m_HighlightTextParticle;
        [SerializeField] Sprite m_GoodSprite;
        [SerializeField] Sprite m_GreatSprite;
        [SerializeField] Sprite m_AmazingSprite;

        private int m_BrokenLines = 0;

        #region Methods

        public void Show(int currentCombo, int brokenLines, int score, GridElementType type)
        {
            m_BrokenLines = brokenLines;

            m_ScoreTextTransform.localPosition = Vector3.zero;
            m_ScoreTextTransform.localScale = Vector2.zero;
            m_ScoreTextTransform.localEulerAngles = new Vector3(0, 0, 7f);
            m_ComboTextTransform.localScale = Vector2.zero;
            m_ComboNumberContainerTransform.localScale = Vector2.zero;

            m_ComboNumberText.text = $"{currentCombo - 1}";

            m_ScoreText.text = string.Format("{0}", score);
            m_ScoreText.color = Color.white;

            switch (m_BrokenLines)
            {
                case 0:
                case 1:
                    break;
                case 2:
                    m_HighlightTextRenderer.sprite = m_GoodSprite;
                    break;
                case 3:
                    m_HighlightTextRenderer.sprite = m_GreatSprite;
                    break;
                default:
                    m_HighlightTextRenderer.sprite = m_AmazingSprite;
                    break;
            }

            var delayTime = 0f;

            var showingSequence = Sequence.Create();

            if (currentCombo > 1)
            {
                var durationComboText = AnimateComboText(ref showingSequence, 0f);

                if (currentCombo > 2)
                {
                    var durationComboNumber = AnimateComboNumber(ref showingSequence, 0.2f);

                    delayTime += durationComboNumber;
                }
                else
                {
                    delayTime += durationComboText;
                }
            }

            AnimateScoreText(ref showingSequence, delayTime);

            if (brokenLines > 1)
                AnimateHighlightText(ref showingSequence, delayTime);

            showingSequence.OnComplete(OnTweenFinished);

            return;

            float AnimateComboText(ref Sequence sequence, float delay)
            {
                sequence.Insert(delay + 0f, Tween.Scale(m_ComboTextTransform, 1f, 0.3f, m_TextScaleInCurve));
                sequence.Insert(delay + 0.8f, Tween.Scale(m_ComboTextTransform, 0f, 0.2f, Ease.InBack));
                return 1f;
            }

            float AnimateComboNumber(ref Sequence sequence, float delay)
            {
                sequence.Insert(delay + 0f, Tween.Scale(m_ComboNumberContainerTransform, 1f, 0.5f, m_TextScaleInCurve));
                sequence.InsertCallback(delay + 0f, () => { m_ComboNumberParticle.Play(); });
                sequence.Insert(delay + 1.0f, Tween.Scale(m_ComboNumberContainerTransform, 0f, 0.3f, m_TextScaleOutCurve));
                return 1.5f;
            }

            void AnimateHighlightText(ref Sequence sequence, float delay)
            {
                PlayHighlightTextSound();

                sequence.Insert(delay + 0f, Tween.Scale(m_HighlightTextTransform,1f, 0.5f, m_TextScaleInCurve));
                sequence.InsertCallback(delay + 0f, () => { m_HighlightTextParticle.Play(); });
                sequence.Insert(delay + 1.0f, Tween.Alpha(m_HighlightTextRenderer, 0f, 0.3f));
                sequence.Insert(delay + 1.0f, Tween.Scale(m_HighlightTextTransform,0f, 0.3f, Ease.InBack));
            }

            void AnimateScoreText(ref Sequence sequence, float delay)
            {
                sequence.Insert(delay + 0f, Tween.Scale(m_ScoreTextTransform,Vector2.one, 0.5f, m_TextScaleInCurve));
                sequence.Insert(delay + 1f, Tween.Scale(m_ScoreTextTransform,Vector2.zero, 0.3f, m_TextScaleOutCurve));
            }
        }

        private void ResetToInitialState()
        {
            m_ScoreText.text = "";
            m_ScoreText.alpha = 1;

            m_ScoreTextTransform.localPosition = Vector3.zero;
            m_ScoreTextTransform.localScale = Vector2.zero;
            m_ComboTextTransform.localScale = Vector2.zero;
            m_ComboNumberContainerTransform.localScale = Vector2.zero;

            m_HighlightTextRenderer.color = new Color(1f, 1f, 1f, 1f);

            m_BrokenLines = 0;
        }

        private void OnTweenFinished()
        {
            ResetToInitialState();

            Core.ScenePool.Recycle(VTransform.gameObject);
        }

        private void PlayHighlightTextSound()
        {
            switch (m_BrokenLines)
            {
                case 0:
                case 1:
                    break;
                case 2:
                    AudioShortcut.PlayVoiceGood();
                    break;
                case 3:
                    AudioShortcut.PlayVoiceGreat();
                    break;
                default:
                    AudioShortcut.PlayVoiceAmazing();
                    break;
            }
        }

        [Button]
        public void TestCombo1()
        {
            Show(2, 1, 50, GridElementType.Yellow);
        }

        [Button]
        public void TestCombo2()
        {
            Show(3, 1, 50, GridElementType.Yellow);
        }

        [Button]
        public void TestCombo1WithHighlightText()
        {
            Show(2, 2, 50, GridElementType.Yellow);
        }

        [Button]
        public void TestCombo2WithHighlightText()
        {
            Show(3, 3, 50, GridElementType.Yellow);
        }

        #endregion
    }
}