using UnityEngine;

namespace OP.BlockPuzzle
{
    public class TouchFXController : MonoBeh<PERSON>our
    {
        [SerializeField] GameObject m_TouchFxPrefab;
        [SerializeField] Camera m_MainCamera;
        [SerializeField] UnityEngine.EventSystems.EventSystem m_EventSystem;

        private void Update()
        {
            if (IsPointerOverUI())
                return;

            if (!Input.GetMouseButtonDown(0)) return;
            Vector2 mousePos = Input.mousePosition;
            Vector2 worldPos = m_MainCamera.ScreenToWorldPoint(mousePos);
            Core.ScenePool.Spawn(m_TouchFxPrefab, null,worldPos, default);
        }


        private bool IsPointerOverUI()
        {
#if UNITY_EDITOR
            return m_EventSystem.IsPointerOverGameObject();
#else
            return m_EventSystem.IsPointerOverGameObject(0);
#endif
        }
    }
}