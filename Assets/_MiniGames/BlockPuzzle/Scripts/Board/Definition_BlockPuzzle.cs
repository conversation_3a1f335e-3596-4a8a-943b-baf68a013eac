using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Sirenix.OdinInspector;
using UnityEditor;
using UnityEngine;

namespace OP.BlockPuzzle
{
    [CreateAssetMenu(menuName = "OnePuz/Definitions/MiniGame_BlockPuzzle")]
    public class Definition_BlockPuzzle : ScriptableObject
    {
        public BrokenElementStyle breakBlockStyle = BrokenElementStyle.Blast;

        public List<int> canFitBlockIdArray;
    
        public List<int> uniqueBlockIdInOneSpawnArray;
        
        public List<BlockGroup> blockGroups;
        
        public List<RulesByDifficulty> rulesByDifficulty;

        public RulesByDifficulty activeRule;
    }
}