using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

namespace OP.BlockPuzzle
{
    public enum FingerTouchState
    {
        PRESSED,
        HOLD,
        RELEASED
    }

    public struct OnPressEvent
    {
        public bool Pressed;
    }

    public class PlayerInput : BaseBehaviour
    {
        #region Constants

        #endregion

        #region Events

        #endregion

        #region Fields

        [SerializeField] private Camera m_MainCamera;

        [Header("Touch Fx")] [SerializeField] GameObject m_TouchFxPrefab;

        #endregion

        #region Properties

        private bool m_Initialized = false;
        private bool m_CanTouch = false;
        private bool m_HasSelected = false;
        private int m_SelectedIndex = -1;
        private string m_SelectedObjectTag = "";

        private int m_CurrentFingerId;

        private FingerTouchState m_CurrentTouchState = FingerTouchState.RELEASED;

        #endregion

        #region Unity Events

        private void Update()
        {
            if (!m_Initialized)
                return;

            if (IsPointerOverUI())
                return;

            if (Application.isEditor)
            {
                Vector2 mousePos = Input.mousePosition;
                if (Input.GetMouseButtonDown(0))
                {
                    HandlePressed(mousePos);
                }

                if (Input.GetMouseButton(0))
                {
                    HandleMove(mousePos);
                }

                if (Input.GetMouseButtonUp(0))
                {
                    HandleRelease(mousePos);
                }
            }
            else
            {
                bool matchedFinger = false;
                if (Input.touchCount == 1)
                {
                    if (m_CurrentTouchState == FingerTouchState.RELEASED)
                        m_CurrentFingerId = Input.GetTouch(0).fingerId;

                    var touch = Input.GetTouch(0);
                    if (m_CurrentFingerId == touch.fingerId)
                    {
                        matchedFinger = true;
                        Vector2 touchPos = touch.position;
                        if (touch.phase == TouchPhase.Began)
                        {
                            m_CurrentTouchState = FingerTouchState.PRESSED;
                            HandlePressed(touchPos);
                        }

                        if (touch.phase == TouchPhase.Moved || touch.phase == TouchPhase.Stationary)
                        {
                            m_CurrentTouchState = FingerTouchState.HOLD;
                            HandleMove(touchPos);
                        }

                        if (touch.phase == TouchPhase.Ended || touch.phase == TouchPhase.Canceled)
                        {
                            m_CurrentTouchState = FingerTouchState.RELEASED;
                            HandleRelease(touchPos);
                        }
                    }
                }

                if ((!matchedFinger || Input.touchCount == 0 || Input.touchCount > 1) && m_CurrentTouchState != FingerTouchState.RELEASED)
                {
                    m_CurrentTouchState = FingerTouchState.RELEASED;
                    m_HasSelected = false;
                    m_SelectedObjectTag = "";

                    if (m_SelectedIndex >= 0)
                        Core.Manager_BlockPuzzle.UnselectBlock(m_SelectedIndex, Vector3.zero, true);
                }
            }
        }

        #endregion

        #region Methods

        public void Configure()
        {
            Input.multiTouchEnabled = false;
            EnableTouch(true);
            m_Initialized = true;
        }

        public void EnableTouch(bool enable)
        {
            m_CanTouch = enable;
        }

        private void HandlePressed(Vector2 touchPos)
        {
            Vector2 worldPos = m_MainCamera.ScreenToWorldPoint(touchPos);
            RaycastHit2D hit = Physics2D.Raycast(worldPos, Vector2.zero);

            if (hit.collider != null)
            {
                if (hit.collider.CompareTag("Selection Area 1"))
                {
                    m_HasSelected = true;
                    m_SelectedIndex = 0;
                    Core.Manager_BlockPuzzle.SelectBlock(m_SelectedIndex, worldPos);
                }
                else if (hit.collider.CompareTag("Selection Area 2"))
                {
                    m_HasSelected = true;
                    m_SelectedIndex = 1;
                    Core.Manager_BlockPuzzle.SelectBlock(m_SelectedIndex, worldPos);
                }
                else if (hit.collider.CompareTag("Selection Area 3"))
                {
                    m_HasSelected = true;
                    m_SelectedIndex = 2;
                    Core.Manager_BlockPuzzle.SelectBlock(m_SelectedIndex, worldPos);
                }
                else
                {
                    m_SelectedObjectTag = hit.collider.tag;
                }
                Core.Event.Fire(new OnPressEvent
                {
                    Pressed = true
                });
            }
            else
            {
                Core.ScenePool.Spawn(m_TouchFxPrefab, null, worldPos, default);
            }
        }

        private void HandleMove(Vector2 touchPos)
        {
            if (m_HasSelected)
            {
                Vector2 worldPos = m_MainCamera.ScreenToWorldPoint(touchPos);
                Core.Manager_BlockPuzzle.MoveBlock(m_SelectedIndex, worldPos);
            }
        }

        private void HandleRelease(Vector2 touchPos)
        {
            if (m_HasSelected)
            {
                Vector2 worldPos = m_MainCamera.ScreenToWorldPoint(touchPos);
                Core.Manager_BlockPuzzle.UnselectBlock(m_SelectedIndex, worldPos, false);

                m_HasSelected = false;
                
                Core.Event.Fire(new OnPressEvent
                {
                    Pressed = false
                });
            }
            else if (!string.IsNullOrEmpty(m_SelectedObjectTag))
            {
                Vector2 worldPos = m_MainCamera.ScreenToWorldPoint(touchPos);
                RaycastHit2D hit = Physics2D.Raycast(worldPos, Vector2.zero);

                if (hit.collider != null && string.Compare(m_SelectedObjectTag, hit.collider.tag) == 0)
                {
                    
                }
            }

            m_SelectedObjectTag = "";
        }

        private bool IsPointerOverUI()
        {
#if UNITY_EDITOR
            return Core.UI.pEventSystem.IsPointerOverGameObject();
#else
            return Core.UI.pEventSystem.IsPointerOverGameObject(0);
#endif
        }

        #endregion
    }
}