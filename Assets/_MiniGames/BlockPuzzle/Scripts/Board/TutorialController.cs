using System.Collections.Generic;
using UnityEngine;
using Newtonsoft.Json;
using OnePuz.Data;
using PrimeTween;

namespace OP.BlockPuzzle
{
    public class TutorialController : BaseBehaviour
    {
        #region Constants

        public readonly string[] TUTORIAL_FILE_NAME_LIST = { "Board_Tutorial_1", "Board_Tutorial_2", "Board_Tutorial_3" };

        #endregion

        #region Events

        #endregion

        #region Fields

        [SerializeField]
        private GameObject m_TutorialLayout;

        [SerializeField]
        private RectTransform m_HighlightRectTrans;

        [SerializeField]
        private RectTransform m_HandRectTrans;

        #endregion

        #region Properties

        private Queue<TutorialData> m_TutorialQueue;
        public int pTutorialQueueCount => m_TutorialQueue.Count;

        private Sequence _handMovingSequence;

        #endregion

        #region Methods

        public void Configure()
        {
            m_TutorialLayout.SetActive(false);
            m_HandRectTrans.gameObject.SetActive(false);

            m_TutorialQueue = new Queue<TutorialData>();
            var shownTutorial = DidShowTutorial();

            if (shownTutorial) return;
            for (var i = 0; i < TUTORIAL_FILE_NAME_LIST.Length; i++)
            {
                var textAsset = Resources.Load<TextAsset>(TUTORIAL_FILE_NAME_LIST[i]);

                if (textAsset == null) continue;
                try
                {
                    var root = JsonConvert.DeserializeObject<TutorialJsonRoot>(textAsset.text);

                    var tutorialData = new TutorialData
                    {
                        boardDataJson = JsonConvert.SerializeObject(root.board),
                        blockDataJson = JsonConvert.SerializeObject(root.blocks),
                        targetPositions = new List<GridCoordinate>()
                    };

                    foreach (var pos in root.targetPositions)
                    {
                        tutorialData.targetPositions.Add(new GridCoordinate(pos.coordX, pos.coordY));
                    }

                    m_TutorialQueue.Enqueue(tutorialData);
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"Failed to load tutorial {TUTORIAL_FILE_NAME_LIST[i]}: {ex.Message}");
                }
            }
        }


        public bool DidShowTutorial()
        {
            return DataShortcut.BlockPuzzle.ShownTutorial;
        }

        public TutorialData GetTutorial()
        {
            if (m_TutorialQueue.Count == 0)
                return null;

            return m_TutorialQueue.Dequeue();
        }

        public void Show(Vector2 targetPos, float width, float height)
        {
            m_TutorialLayout.SetActive(true);

            m_HighlightRectTrans.position = new Vector3(targetPos.x, targetPos.y, 0f);
            m_HighlightRectTrans.sizeDelta = new Vector2(width, height);
        }

        public void Hide()
        {
            HideHand();
            m_TutorialLayout.SetActive(false);
        }

        public void AnimateMovingHand(Vector2 blockPosition)
        {
            _handMovingSequence.Stop();
            m_HandRectTrans.gameObject.SetActive(true);
            var startPos = blockPosition + new Vector2(1, -1);
            m_HandRectTrans.position = startPos;
            m_HandRectTrans.localScale = Vector3.one * 1.2f;

            _handMovingSequence = Sequence.Create(cycles: -1);
            _handMovingSequence.Chain(Tween.Position(m_HandRectTrans, blockPosition, 1f));
            _handMovingSequence.Chain(Tween.Scale(m_HandRectTrans, Vector3.one * 1.1f, 0.5f, Ease.OutBack));
            _handMovingSequence.Chain(Tween.Position(m_HandRectTrans, m_HighlightRectTrans.position, 1.5f));
            _handMovingSequence.Chain(Tween.Scale(m_HandRectTrans, Vector3.one * 1.2f, 0.5f, Ease.OutBack));
            _handMovingSequence.Chain(Tween.Position(m_HandRectTrans, startPos, 2f));
        }

        public void AnimatePressingHand(Vector2 targetPosition)
        {
            _handMovingSequence.Stop();
            m_HandRectTrans.gameObject.SetActive(true);
            var startPos = targetPosition + new Vector2(1, -1);
            m_HandRectTrans.position = startPos;
            m_HandRectTrans.localScale = Vector3.one * 1.2f;

            _handMovingSequence = Sequence.Create(cycles: -1);
            _handMovingSequence.Chain(Tween.Position(m_HandRectTrans, targetPosition, 1f));
            _handMovingSequence.Chain(Tween.Scale(m_HandRectTrans, Vector3.one * 1.1f, 0.5f, Ease.OutBack));
            _handMovingSequence.Chain(Tween.Scale(m_HandRectTrans, Vector3.one * 1.2f, 0.5f, Ease.OutBack));
            _handMovingSequence.Chain(Tween.Position(m_HandRectTrans, startPos, 1f));
        }

        public void HideHand()
        {
            _handMovingSequence.Stop();
            m_HandRectTrans.gameObject.SetActive(false);
        }

        #endregion
    }

    public class TutorialData
    {
        public string boardDataJson;
        public string blockDataJson;
        public List<GridCoordinate> targetPositions;
    }

    public class TutorialJsonRoot
    {
        public BoardSaveModelDto board { get; set; }
        public BlockSaveModelDto blocks { get; set; }
        public List<GridCoordinateDto> targetPositions { get; set; }
    }
}