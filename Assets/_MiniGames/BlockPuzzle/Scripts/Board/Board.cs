using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using Newtonsoft.Json;
using OnePuz.Audio;
using OnePuz.Data;
using UnityEngine;
using UnityEngine.Serialization;

namespace OP.BlockPuzzle
{
    public class Board : BaseBehaviour
    {
        private static Board _instance;

        #region Constants

        private int _gridNumber = 10;
        public static int GridNumber => _instance._gridNumber;
        private float _gridSize = 10f;
        private float _blockSize = 1f;
        public static float BlockSize => _instance._blockSize;
        private bool _colorConfigure;

        #endregion

        #region Fields

        [FormerlySerializedAs("m_Container")] [SerializeField]
        private Transform mContainer;

        [FormerlySerializedAs("m_GridElementPrefab")] [SerializeField]
        private GameObject mGridElementPrefab;

        [SerializeField] private SpriteRenderer backGroundSpriteRenderer;

        #endregion

        #region Properties

        public GridElement[,] Grid { get; private set; }

        public Dictionary<GridElementType, int> TypeCountDictionary { get; private set; }

        #endregion
        
        private void Awake()
        {
            _instance = this;
        }

        #region Methods

        public void Configure(Action onComplete)
        {
            _gridNumber = 10;
            _gridSize = 10;
            _blockSize = 1;

            StartCoroutine(DoConfigureBoard(onComplete));
        }

        private IEnumerator DoConfigureBoard(Action onComplete)
        {
            TypeCountDictionary = new Dictionary<GridElementType, int>();
            for (var i = 1; i < Core.Manager_BlockPuzzle?.BlockTypeCount + 1; i++)
                TypeCountDictionary[(GridElementType)i] = 0;

            Grid = new GridElement[_gridNumber, _gridNumber];
            for (var x = 0; x < _gridNumber; x++)
            {
                for (var y = 0; y < _gridNumber; y++)
                {
                    var gridElement = Instantiate(mGridElementPrefab).GetComponent<GridElement>();
                    gridElement.VTransform.SetParent(mContainer);
                    gridElement.VTransform.localPosition = GetLocalPositionByGridIndex(x, y);
                    gridElement.Configure(new GridCoordinate(x, y));
                    Grid[x, y] = gridElement;
                }

                yield return null;
            }

            onComplete?.Invoke();
        }

        public GridElement GetElement(int x, int y)
        {
            if (x < 0 || x >= _gridNumber || y < 0 || y >= _gridNumber)
                return null;

            return Grid[x, y];
        }

        public void ResetAllElements()
        {
            for (int x = 0; x < _gridNumber; x++)
            {
                for (int y = 0; y < _gridNumber; y++)
                {
                    Grid[x, y].ClearData();
                    Grid[x, y].ResetRender();
                    Grid[x, y].ResetBreakingOrder();
                }
            }
        }

        public void Replay(Action onComplete)
        {
            StartCoroutine(DoReplay(onComplete));
        }

        private IEnumerator DoReplay(Action onComplete)
        {
            for (int i = 0; i < _gridNumber * 2 - 1; i++)
            {
                for (int x = 0, y = i; x <= i && y >= 0; x++, y--)
                {
                    if (x < 0 || x >= _gridNumber || y >= _gridNumber)
                        continue;

                    Grid[x, y].AnimateClean();
                }

                AudioShortcut.PlaySpin();
            }

            yield return null;
            onComplete?.Invoke();
        }

        public void UnhighlightAllElements()
        {
            for (var x = 0; x < _gridNumber; x++)
            {
                for (var y = 0; y < _gridNumber; y++)
                {
                    Grid[x, y].Unhighlight();
                }
            }
        }

        public void ResetBreakingOrderForAllElements()
        {
            for (int x = 0; x < _gridNumber; x++)
            {
                for (int y = 0; y < _gridNumber; y++)
                {
                    Grid[x, y].ResetBreakingOrder();
                }
            }
        }

        public bool CheckIfBlockFitSlots(Block block, List<GridCoordinate> slots)
        {
            var relativeGridCoords = block.RelativeGridCoordinates;
            if (relativeGridCoords.Count != slots.Count)
                return false;

            for (var i = 0; i < slots.Count; i++)
            {
                var validGridCoord = true;
                for (var j = 0; j < block.RelativeGridCoordinates.Count; j++)
                {
                    var coordX = slots[i].x + block.RelativeGridCoordinates[j].x;
                    var coordY = slots[i].y + block.RelativeGridCoordinates[j].y;
                    if (coordX < 0 || coordX >= _gridNumber || coordY < 0 || coordY >= _gridNumber)
                    {
                        validGridCoord = false;
                        break;
                    }

                    if (Grid[coordX, coordY].typeData == null) continue;
                    validGridCoord = false;
                    break;
                }

                if (!validGridCoord) continue;
                return true;
            }

            return false;
        }

        public bool CheckIfAvailableForBlock(List<GridCoordinate> relativeGridCoordinates)
        {
            for (var x = 0; x < _gridNumber; x++)
            {
                for (var y = 0; y < _gridNumber; y++)
                {
                    var element = Grid[x, y];
                    if (element.typeData != null)
                    {
                        // OLogger.LogError($"{element.name} has no type data: {element.typeData}");
                        continue;
                    }

                    var validGridCoord = true;
                    for (var i = 0; i < relativeGridCoordinates.Count; i++)
                    {
                        var coordX = x + relativeGridCoordinates[i].x;
                        var coordY = 0;
                        coordY = y + relativeGridCoordinates[i].y;

                        // OLogger.LogNotice($"Checking X: {x} {relativeGridCoordinates[i].x} {coordX}");
                        // OLogger.LogNotice($"Checking Y: {y} {relativeGridCoordinates[i].y} {coordY}");
                        if (coordX < 0 || coordX >= _gridNumber || coordY < 0 || coordY >= _gridNumber)
                        {
                            validGridCoord = false;
                            // OLogger.LogError($"{element.name}-{coordX}-{coordY} has invalid coordinate {coordX} {coordY} {_gridNumber}");
                            break;
                        }

                        if (Grid[coordX, coordY].typeData == null)
                        {
                            continue;
                        }

                        // OLogger.LogError($"{element.name}-{coordX}-{coordY} has no type data: {element.typeData}");
                        validGridCoord = false;
                        break;
                    }

                    if (!validGridCoord) continue;
                    return true;
                }
            }

            return false;
        }

        public void ConfigureTypeCountCollection()
        {
            for (int i = 1; i < Core.Manager_BlockPuzzle.BlockTypeCount + 1; i++)
                TypeCountDictionary[(GridElementType)i] = 0;

            for (int x = 0; x < _gridNumber; x++)
            {
                for (int y = 0; y < _gridNumber; y++)
                {
                    if (Grid[x, y].typeData == null)
                        continue;

                    var type = Grid[x, y].typeData.type;
                    TypeCountDictionary.TryAdd(type, 0);

                    TypeCountDictionary[type]++;
                }
            }
        }

        public void HandleLose(Action onComplete)
        {
            StartCoroutine(DoHandleLose(onComplete));
        }

        private IEnumerator DoHandleLose(Action onComplete)
        {
            var grayBlockSprite =
                Core.Manager_BlockPuzzle.GridElementTypeConfigures.GetDataByType(GridElementType.Gray).icon;
            var grayBlockWithStarSprite = Core.Manager_BlockPuzzle.GridElementTypeConfigures
                .GetDataByType(GridElementType.Gray).iconWithStar;
            var rnd = new System.Random();
            var arrayX = Enumerable.Range(0, _gridNumber).OrderBy(_ => rnd.Next()).ToArray();
            var arrayY = Enumerable.Range(0, _gridNumber).OrderBy(_ => rnd.Next()).ToArray();
            var delay = new WaitForSeconds(0.05f);
            for (var x = 0; x < _gridNumber; x++)
            {
                for (var y = 0; y < _gridNumber; y++)
                {
                    var element = Grid[arrayX[x], arrayY[y]];
                    if (element.typeData == null) continue;
                    element.HandleLose(grayBlockSprite, grayBlockWithStarSprite);
                    yield return delay;
                }
            }

            yield return new WaitForSeconds(0.5f);

            // Clear saved data
            DataShortcut.BlockPuzzle.BoardData = null;
            yield return null;
            onComplete?.Invoke();
        }

        private readonly HashSet<GridCoordinate> _mCheckingBrokenElementCoordinates = new();
        private readonly List<GridElement> _mCheckingNeighborElements = new(64);
        private readonly List<Vector2Int> _mCheckingBrokenLines = new(10);

        public HashSet<GridCoordinate> HandleBreakingElements(List<GridElement> checkingElements,
            GridElementTypeData data, out int brokenLineCount)
        {
            brokenLineCount = 0;
            _mCheckingBrokenElementCoordinates.Clear();
            _mCheckingBrokenLines.Clear();

            for (var i = 0; i < checkingElements.Count; i++)
            {
                checkingElements[i].brokenState = BrokenElementState.MAIN_BROKEN_ELEMENT;
            }

            for (var i = 0; i < checkingElements.Count; i++)
            {
                var element = checkingElements[i];
                var coordinate = element.coordinate;
                // Horizontal checking
                var canBreakHorizontal = true;
                for (int x1 = coordinate.x, x2 = coordinate.x, breakingOrder = 0;
                     x1 >= 0 || x2 < _gridNumber;
                     x1--, x2++, breakingOrder++)
                {
                    _mCheckingNeighborElements.Clear();
                    if (x1 >= 0)
                        _mCheckingNeighborElements.Add(Grid[x1, coordinate.y]);

                    if (x2 < _gridNumber)
                        _mCheckingNeighborElements.Add(Grid[x2, coordinate.y]);

                    for (var neighborIndex = 0; neighborIndex < _mCheckingNeighborElements.Count; neighborIndex++)
                    {
                        var neighborElement = _mCheckingNeighborElements[neighborIndex];
                        if (neighborElement.typeData != null)
                        {
                            // Do nothing
                        }
                        else if (neighborElement.brokenState == BrokenElementState.MAIN_BROKEN_ELEMENT)
                        {
                            neighborElement.breakingOrder = 0;
                        }
                        else
                        {
                            canBreakHorizontal = false;
                            break;
                        }

                        if (neighborElement.breakingOrder < 0 || neighborElement.breakingOrder > breakingOrder)
                            neighborElement.breakingOrder = breakingOrder;
                    }

                    if (!canBreakHorizontal)
                        break;
                }

                var row = new Vector2Int(-1, element.coordinate.y);
                if (canBreakHorizontal && !_mCheckingBrokenLines.Contains(row))
                {
                    _mCheckingBrokenLines.Add(row);

                    for (var x = 0; x < _gridNumber; x++)
                    {
                        var neighborElement = Grid[x, element.coordinate.y];
                        neighborElement.Highlight(data);
                        if (neighborElement.brokenState == BrokenElementState.NONE)
                            neighborElement.brokenState = BrokenElementState.RELATIVE_BROKEN_ELEMENT;

                        _mCheckingBrokenElementCoordinates.Add(neighborElement.coordinate);

                        var newBreakingOrder = neighborElement.coordinate.x;
                        neighborElement.breakingOrderWoody = neighborElement.breakingOrderWoody >= 0
                            ? Mathf.Min(neighborElement.breakingOrderWoody, newBreakingOrder)
                            : newBreakingOrder;
                    }
                }

                for (var x = 0; x < _gridNumber; x++)
                {
                    var resetElement = Grid[x, element.coordinate.y];
                    if (!canBreakHorizontal && resetElement.state != GridElementState.EMPTY_HIGHLIGHT)
                        if (resetElement.brokenState == BrokenElementState.NONE)
                            resetElement.breakingOrder = -1;

                    if (resetElement.brokenState == BrokenElementState.RELATIVE_BROKEN_ELEMENT)
                        resetElement.brokenState = BrokenElementState.NONE;
                }

                // Vertical checking
                var canBreakVertical = true;
                for (int y1 = coordinate.y, y2 = coordinate.y, breakingOrder = 0;
                     y1 >= 0 || y2 < _gridNumber;
                     y1--, y2++, breakingOrder++)
                {
                    _mCheckingNeighborElements.Clear();
                    if (y1 >= 0)
                        _mCheckingNeighborElements.Add(Grid[coordinate.x, y1]);

                    if (y2 < _gridNumber)
                        _mCheckingNeighborElements.Add(Grid[coordinate.x, y2]);

                    for (var neighborIndex = 0; neighborIndex < _mCheckingNeighborElements.Count; neighborIndex++)
                    {
                        var neighborElement = _mCheckingNeighborElements[neighborIndex];
                        if (neighborElement.typeData != null)
                        {
                            // Do nothing
                        }
                        else if (neighborElement.brokenState == BrokenElementState.MAIN_BROKEN_ELEMENT)
                        {
                            neighborElement.breakingOrder = 0;
                        }
                        else
                        {
                            canBreakVertical = false;
                            break;
                        }

                        if (neighborElement.breakingOrder < 0 || neighborElement.breakingOrder > breakingOrder)
                            neighborElement.breakingOrder = breakingOrder;
                    }

                    if (!canBreakVertical)
                        break;
                }

                var col = new Vector2Int(element.coordinate.x, -1);
                if (canBreakVertical && !_mCheckingBrokenLines.Contains(col))
                {
                    _mCheckingBrokenLines.Add(col);

                    for (int y = 0; y < _gridNumber; y++)
                    {
                        var neighborElement = Grid[element.coordinate.x, y];
                        neighborElement.Highlight(data);
                        if (neighborElement.brokenState == BrokenElementState.NONE)
                            neighborElement.brokenState = BrokenElementState.RELATIVE_BROKEN_ELEMENT;

                        _mCheckingBrokenElementCoordinates.Add(neighborElement.coordinate);

                        var newBreakingOrder = _gridNumber - neighborElement.coordinate.y;
                        neighborElement.breakingOrderWoody = neighborElement.breakingOrderWoody >= 0
                            ? Mathf.Min(neighborElement.breakingOrderWoody, newBreakingOrder)
                            : newBreakingOrder;
                    }
                }

                for (int y = 0; y < _gridNumber; y++)
                {
                    var resetElement = Grid[element.coordinate.x, y];
                    if (!canBreakVertical && resetElement.state != GridElementState.EMPTY_HIGHLIGHT)
                        if (resetElement.brokenState == BrokenElementState.NONE)
                        {
                            resetElement.breakingOrder = -1;
                            resetElement.breakingOrderWoody = -1;
                        }

                    if (resetElement.brokenState == BrokenElementState.RELATIVE_BROKEN_ELEMENT)
                        resetElement.brokenState = BrokenElementState.NONE;
                }
            }

            brokenLineCount = _mCheckingBrokenLines.Count;

            for (var i = 0; i < checkingElements.Count; i++)
            {
                checkingElements[i].brokenState = BrokenElementState.NONE;
            }

            return _mCheckingBrokenElementCoordinates;
        }

        public void CheckElementsCanBreak(GridElement[] checkingElements, int count, out int brokenLineCount)
        {
            brokenLineCount = 0;
            _mCheckingBrokenLines.Clear();

            for (int i = 0; i < count; i++)
            {
                checkingElements[i].brokenState = BrokenElementState.MAIN_BROKEN_ELEMENT;
            }

            for (int i = 0; i < count; i++)
            {
                var element = checkingElements[i];

                var canBreakHorizontal = true;
                var canBreakVertical = true;
                for (int coordIndex = 0; coordIndex < _gridNumber; coordIndex++)
                {
                    var horizontalElement = Grid[coordIndex, element.coordinate.y];
                    var verticalElement = Grid[element.coordinate.x, coordIndex];

                    if (horizontalElement.typeData == null &&
                        horizontalElement.brokenState != BrokenElementState.MAIN_BROKEN_ELEMENT)
                        canBreakHorizontal = false;

                    if (verticalElement.typeData == null &&
                        verticalElement.brokenState != BrokenElementState.MAIN_BROKEN_ELEMENT)
                        canBreakVertical = false;
                }

                if (canBreakHorizontal)
                {
                    var row = new Vector2Int(-1, element.coordinate.y);
                    var alreadyBrokenRow = false;
                    for (var lineIndex = 0; lineIndex < _mCheckingBrokenLines.Count; lineIndex++)
                    {
                        if (!_mCheckingBrokenLines[lineIndex].Equals(row)) continue;
                        alreadyBrokenRow = true;
                        break;
                    }

                    if (!alreadyBrokenRow)
                    {
                        _mCheckingBrokenLines.Add(row);
                    }
                }

                if (!canBreakVertical) continue;
                {
                    var col = new Vector2Int(element.coordinate.x, -1);
                    var alreadyBrokenCol = false;
                    for (int lineIndex = 0; lineIndex < _mCheckingBrokenLines.Count; lineIndex++)
                    {
                        if (!_mCheckingBrokenLines[lineIndex].Equals(col)) continue;
                        alreadyBrokenCol = true;
                        break;
                    }

                    if (!alreadyBrokenCol)
                    {
                        _mCheckingBrokenLines.Add(col);
                    }
                }
            }

            brokenLineCount = _mCheckingBrokenLines.Count;

            // Reset grids we are checking on. We got what we want :D 
            for (var i = 0; i < count; i++)
            {
                checkingElements[i].brokenState = BrokenElementState.NONE;
            }
        }

        private Vector2 GetLocalPositionByGridIndex(int x, int y)
        {
            var startPosX = -_gridSize / 2f + _blockSize / 2f;
            var startPosY = -_gridSize / 2f + _blockSize / 2f;

            return new Vector2(startPosX + x * _blockSize, startPosY + y * _blockSize);
        }

        public Vector3 GetWorldPositionByGridIndex(int x, int y)
        {
            return mContainer.TransformPoint(GetLocalPositionByGridIndex(x, y));
        }

        public bool TryGetElementByWorldPosition(Vector3 worldPosition, out GridElement element)
        {
            element = null;

            Vector2 localPos = mContainer.InverseTransformPoint(worldPosition);

            var halfGrid = _gridNumber * _blockSize * 0.5f;

            if (localPos.x < -halfGrid || localPos.x >= halfGrid ||
                localPos.y < -halfGrid || localPos.y >= halfGrid)
            {
                return false;
            }

            var startX = -halfGrid;
            var startY = -halfGrid;

            var relX = (localPos.x - startX) / _blockSize;
            var relY = (localPos.y - startY) / _blockSize;

            var ix = Mathf.FloorToInt(relX);
            var iy = Mathf.FloorToInt(relY);

            var y = iy;

            if (ix < 0 || ix >= _gridNumber || y < 0 || y >= _gridNumber)
                return false;

            element = Grid[ix, y];
            return true;
        }

        #region Board Archives

        public void LoadBoardData()
        {
            LoadBoardDataFromDto(DataShortcut.BlockPuzzle.BoardData);
        }

        public void LoadBoardDataFromDto(BoardSaveModelDto boardData)
        {
            OLogger.LogNotice("Loading board data...");
            if (boardData == null) return;
            
            foreach (var tile in boardData.grid)
            {
                var data = Core.Manager_BlockPuzzle.GridElementTypeConfigures.GetDataByType((GridElementType)tile.type);
                Grid[tile.coordX, tile.coordY].FillWithData(data);
                if (tile.containStar)
                {
                    Grid[tile.coordX, tile.coordY].FillStar();
                }
            }
        }

        public void SaveBoardData()
        {
            var saveModel = new BoardSaveModelDto { grid = new List<GridTileDto>() };

            for (var x = 0; x < _gridNumber; x++)
            {
                for (var y = 0; y < _gridNumber; y++)
                {
                    var element = Grid[x, y];
                    if (element.typeData != null)
                    {
                        saveModel.grid.Add(new GridTileDto
                        {
                            coordX = x,
                            coordY = y,
                            type = (int)element.typeData.type,
                            containStar = element.containStar
                        });
                    }
                }
            }
            DataShortcut.BlockPuzzle.BoardData = saveModel;
        }

        #endregion

        #endregion
    }
}