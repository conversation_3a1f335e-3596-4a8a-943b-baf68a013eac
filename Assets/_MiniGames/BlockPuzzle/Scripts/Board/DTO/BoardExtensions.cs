using System.Collections.Generic;
using OP.BlockPuzzle;
using Newtonsoft.Json;
using OnePuz.Data;

public static class BoardExtensions
{
    public static string GetBoardData(this Board board)
    {
        var saveModel = new BoardSaveModelDto { grid = new List<GridTileDto>() };

        for (int x = 0; x < Board.GridNumber; x++)
        {
            for (int y = 0; y < Board.GridNumber; y++)
            {
                var element = board.Grid[x, y];
                if (element.typeData != null)
                {
                    saveModel.grid.Add(new GridTileDto
                    {
                        coordX = x,
                        coordY = y,
                        type = (int)element.typeData.type,
                        containStar = element.containStar
                    });
                }
            }
        }

        return JsonConvert.SerializeObject(saveModel);
    }

    public static void LoadBoardDataFromDto(this Board board, BoardSaveModelDto data)
    {
        foreach (var tile in data.grid)
        {
            var typeData = Core.Manager_BlockPuzzle.GridElementTypeConfigures.GetDataByType((GridElementType)tile.type);
            board.Grid[tile.coordX, tile.coordY].FillWithData(typeData);
            if (tile.containStar)
            {
                board.Grid[tile.coordX, tile.coordY].FillStar();
            }
        }
    }
}