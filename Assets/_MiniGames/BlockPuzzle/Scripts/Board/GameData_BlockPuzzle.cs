using Newtonsoft.Json;
using OnePuz.Data;
using OnePuz.Data.Services;
using UnityEngine.Serialization;

namespace OP.BlockPuzzle
{
    public class GameData_BlockPuzzle : ISaveData, IVersionedData
    {
        [JsonProperty]
        public bool ShownTutorial { get; set; }
        
        [JsonProperty]
        public BlockSaveModelDto BlockData { get; set; }
        
        [JsonProperty]
        public BoardSaveModelDto BoardData { get; set; }

        #region Score & Combo

        [JsonProperty] private int _currentScore;
        [JsonProperty] private int _highScore;
        [JsonProperty] private int _currentCombo;

        [JsonIgnore]
        public int CurrentScore
        {
            get => _currentScore;
            private set => _currentScore = value;
        }

        [JsonIgnore]
        public int HighScore
        {
            get => _highScore;
            private set => _highScore = value;
        }

        [JsonIgnore]
        public int CurrentCombo
        {
            get => _currentCombo;
            private set => _currentCombo = value;
        }

        public void SetScore(int score)
        {
            CurrentScore = score;
            var isNewHighScore = false;
            if (CurrentScore > HighScore)
            {
                SetHighScore(CurrentScore);
                isNewHighScore = true;
            }

            Core.Event.Fire(new OnScoreChangeEvent
            {
                CurrentScore = CurrentScore,
                IsNewHighScore = isNewHighScore
            });
        }

        public void AddScore(int bonusScore)
        {
            SetScore(CurrentScore + bonusScore);
        }

        public void SetHighScore(int score)
        {
            HighScore = score;
        }

        public void SetCombo(int combo)
        {
            CurrentCombo = combo;
        }

        public void AddCombo(int bonusCombo)
        {
            SetCombo(CurrentCombo + bonusCombo);
        }

        public struct OnScoreChangeEvent
        {
            public int CurrentScore;
            public bool IsNewHighScore;
        }

        #endregion

        public override void SetupDefaultValues()
        {
        }

        public int Version { get; set; }

        public void Migrate()
        {
        }
    }
}