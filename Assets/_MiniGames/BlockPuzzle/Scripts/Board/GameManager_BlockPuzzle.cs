using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Cysharp.Threading.Tasks;
using Newtonsoft.Json;
using OnePuz;
using OnePuz.Audio;
using OnePuz.Data;
using OnePuz.Extensions;
using OnePuz.Managers;
using OnePuz.UI;
using Sirenix.OdinInspector;

namespace OP.BlockPuzzle
{
    public class GameManager_BlockPuzzle : BaseManager
    {
        [SerializeField]
        private Board _board;

        [SerializeField]
        private BlockSpawner _spawner;

        [SerializeField]
        private PlayerInput _input;

        [SerializeField]
        private TutorialController _tutorial;

        [SerializeField]
        private ScoreController _score;

        [SerializeField]
        private Camera _camera;

        [SerializeField]
        private Transform _boardTransform;

        [SerializeField]
        private Transform _blockGroupTransform;

        [Header("Configures")] [SerializeField]
        private GridElementTypeConfigures _gridElementTypeConfigures;

        [Header("Breaking Effect Prefabs")] [SerializeField]
        private GameObject _breakingEffectPrefab;

        public GameObject BreakingEffectPrefab => _breakingEffectPrefab;

        [SerializeField]
        private GridElementBreakingFXBrickStyleSettings _breakingEffectBrickStyleConfigs;

        public Board Board => _board;

        public PlayerInput Input => _input;

        public GridElementTypeConfigures GridElementTypeConfigures => _gridElementTypeConfigures;

        public int BlockTypeCount => _gridElementTypeConfigures.GetBlockTypeCount();

        private List<GridElement> _checkingElements;
        private HashSet<GridCoordinate> _willBreakElementCoordinates;
        private readonly List<Vector2> _willBreakElementInBlockPositions = new();
        private int _brokenLines;
        private bool _canPlaceBlock;
        private bool _placingBlock;
        private bool _isGameStarted;

        private TutorialData _currentTutorialData;

        private void OnEnable()
        {
            this.EventSubscribe<GameStateChangedEvent>(OnGameStateChanged);
        }

        private void OnGameStateChanged(GameStateChangedEvent e)
        {
            
        }
        
        public override async UniTask LoadAsync()
        {
            var boardConfigured = false;
            var spawnerConfigured = false;

            _board.Configure(() => { boardConfigured = true; });
            _spawner.Configure(() => { spawnerConfigured = true; });
            
            await UniTask.WaitUntil(() => boardConfigured && spawnerConfigured);

            _input.Configure();
            _input.EnableTouch(false);

            _checkingElements = new List<GridElement>();
            _willBreakElementCoordinates = new HashSet<GridCoordinate>();
            _brokenLines = 0;

            _score.LoadSavedData();

            _tutorial.Configure();
            HandleTutorial();
            if (_currentTutorialData == null)
            {
                _board.LoadBoardData();
                _spawner.LoadSavedBlocks();
            }

            CheckAvailableSpaceForBlocks();
            await UniTask.Yield();

            _spawner.ShowBlocks();
            _input.EnableTouch(true);
        }

        public override async UniTask UnloadAsync()
        {
            _board.SaveBoardData();
            _spawner.SaveBlocks();
            await UniTask.Yield();
        }

        public override async UniTask ResetAsync()
        {
            var animatingReplay = true;
            _board.Replay(() => { animatingReplay = false; });
            await UniTask.WaitUntil(() => !animatingReplay);
            
            _spawner.Reset();
            _spawner.SpawnAllBlocks();
            
            _canPlaceBlock = true;
            _placingBlock = false;
        }

        public override void Activate()
        {
            _input.EnableTouch(true);
            _spawner.ShowBlocks();
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            var state = Core.State.CurrentState;

            if (!hasFocus && state == GameState.PLAYING)
            {
                _board.SaveBoardData();
                _spawner.SaveBlocks();
            }

            if (!hasFocus) return;

            if (state == GameState.PLAYING)
            {
                MoveBackAllBlocks();
            }
        }

        private void LoseGame()
        {
            StartCoroutine(DoLoseGame());
        }

        private IEnumerator DoLoseGame()
        {
            AudioShortcut.PlayLose();

            MoveBackAllBlocks();

            _score.HandleLose();
            _spawner.HandleLose();

            var loseAnimating = true;
            _board.HandleLose(() => { loseAnimating = false; });

            while (loseAnimating)
                yield return null;

            Core.Lose();
        }

        #region Block Action

        public void SelectBlock(int index, Vector3 worldPos)
        {
            var block = _spawner.GetBlock(index);
            if (block == null) return;
            AudioShortcut.PlayChooseBlock();
            block.HandleSelected(worldPos);

            if (_currentTutorialData != null)
            {
                _tutorial.HideHand();
            }

            _spawner.HandleSelectedBlock();
        }

        public void MoveBlock(int index, Vector3 worldPos)
        {
            var block = _spawner.GetBlock(index);
            if (block)
            {
                HandleMovingBlock(block, worldPos);
            }
        }

        private void HandleMovingBlock(Block block, Vector3 worldPos)
        {
            block.HandleMoving(worldPos);
            block.Highlight(false);

            for (var i = 0; i < block.elements.Count; i++)
            {
                var elementWillBreak = false;
                if (_board.TryGetElementByWorldPosition(block.elements[i].VTransform.position,
                        out var element))
                {
                    foreach (var coordinate in _willBreakElementCoordinates)
                    {
                        if (element.coordinate.Compare(coordinate))
                        {
                            elementWillBreak = true;
                            break;
                        }
                    }
                }

                block.HighlightSingle(i, elementWillBreak);
            }

            if (_placingBlock)
                return;

            _board.UnhighlightAllElements();
            _checkingElements.Clear();
            _willBreakElementCoordinates.Clear();
            for (int i = 0; i < block.elements.Count; i++)
            {
                if (!_board.TryGetElementByWorldPosition(block.elements[i].VTransform.position,
                        out var element))
                    continue;

                bool containsElement = false;
                for (int j = 0; j < _checkingElements.Count; j++)
                {
                    if (_checkingElements[j].coordinate.Compare(element.coordinate))
                    {
                        containsElement = true;
                        break;
                    }
                }

                if (!containsElement && element.typeData == null)
                {
                    _checkingElements.Add(element);
                }
            }

            if (_checkingElements.Count != block.elements.Count)
            {
                _canPlaceBlock = false;
                return;
            }

            HighlightCheckingElements(block.TypeData);

            if (_currentTutorialData != null)
            {
                if (_checkingElements.Count != _currentTutorialData.targetPositions.Count)
                {
                    _canPlaceBlock = false;
                    return;
                }

                bool validPositionForTutorial = true;
                for (int i = 0; i < _checkingElements.Count; i++)
                {
                    bool contains = false;
                    for (int j = 0; j < _currentTutorialData.targetPositions.Count; j++)
                    {
                        contains = _checkingElements[i].coordinate.Compare(_currentTutorialData.targetPositions[j]);
                        if (contains)
                            break;
                    }

                    if (!contains)
                    {
                        validPositionForTutorial = false;
                        break;
                    }
                }

                if (!validPositionForTutorial)
                {
                    _canPlaceBlock = false;
                    return;
                }
            }

            _willBreakElementCoordinates.UnionWith(_board.HandleBreakingElements(_checkingElements, block.TypeData,
                out _brokenLines));
            _canPlaceBlock = true;
        }

        public void UnselectBlock(int index, Vector3 worldPos, bool hasCanceled)
        {
            var block = _spawner.GetBlock(index);
            // var block = shapeSpawner.GetBlock(index);
            if (block == null) return;
            block.HandleRelease();
            if (_canPlaceBlock && !_placingBlock && !hasCanceled && _checkingElements.Count > 0)
            {
                _placingBlock = true;
                _canPlaceBlock = false;

                StartCoroutine(DoPlaceBlock(index));

                if (_currentTutorialData != null)
                {
                    _tutorial.Hide();
                }
            }
            else
            {
                AudioShortcut.PlayPlacedFailed();

                UnhighlightCheckingElements();
                block.MoveBack();
                _checkingElements.Clear();
                _brokenLines = 0;

                if (_currentTutorialData != null)
                {
                    ShowTutorialAnimation(block);
                }
            }
        }

        private void MoveBackAllBlocks()
        {
            for (int i = 0; i < 3; i++)
                MoveBackBlock(i);
        }

        private void MoveBackBlock(int index)
        {
            var block = _spawner.GetBlock(index);
            // var block = shapeSpawner.GetBlock(index);
            if (block == null) return;
            UnhighlightCheckingElements();
            block.MoveBack();
            _checkingElements.Clear();
            _brokenLines = 0;
        }

        private IEnumerator DoPlaceBlock(int index)
        {
            var block = _spawner.GetBlock(index);
            _spawner.RemoveBlock(index);
            var typeData = block.TypeData;

            _willBreakElementInBlockPositions.Clear();

            var localCheckingElements = new List<GridElement>(_checkingElements);
            _checkingElements.Clear();
            for (var i = 0; i < localCheckingElements.Count; i++)
                localCheckingElements[i].Unhighlight();

            var gotStarElements = GetGridCellGotStarByBlock(block);

            _input.EnableTouch(false);

            if (_willBreakElementCoordinates.Count == 0)
                AudioShortcut.PlayPlacedBlock();

            _score.AddScore(block.elements.Count);

            var offset = localCheckingElements[0].VTransform.position - block.elements[0].VTransform.position;
            var targetPos = block.VTransform.position + offset;
            var blockMoving = true;
            block.PlaceBlock(targetPos, () => { blockMoving = false; });
            while (blockMoving)
                yield return null;

            var animating = true;
            block.ScaleWithChildren(1, Board.BlockSize * 1, 0.1f, () => { animating = false; });
            yield return new WaitUntil(() => !animating);

            for (var i = 0; i < localCheckingElements.Count; i++)
            {
                var containStar = false;
                var checkingElement = localCheckingElements[i];
                for (int ii = 0; ii < gotStarElements.Count; ii++)
                {
                    if (gotStarElements[ii] != checkingElement) continue;
                    containStar = true;
                    break;
                }

                var willBreakSoon = false;
                foreach (var coordinate in _willBreakElementCoordinates)
                {
                    if (!checkingElement.coordinate.Compare(coordinate)) continue;
                    willBreakSoon = true;
                    break;
                }

                checkingElement.FillWithData(block.TypeData, containStar, willBreakSoon);

                if (willBreakSoon)
                    _willBreakElementInBlockPositions.Add(checkingElement.VTransform.position);
            }


            _input.EnableTouch(true);

            block.Reset();
            Core.ScenePool.Recycle(block.VTransform.gameObject);

            if (_willBreakElementCoordinates.Count > 0)
            {
                var breakColumnDictionary = new Dictionary<int, int>();
                var breakRowDictionary = new Dictionary<int, int>();

                var listOrder = _willBreakElementCoordinates.OrderByDescending(x => x.y).ToList();
                foreach (var coordinate in listOrder)
                {
                    var gridElement = _board.GetElement(coordinate.x, coordinate.y);

                    gridElement.Break(typeData, _brokenLines);

                    breakColumnDictionary.TryAdd(coordinate.x, 0);

                    breakRowDictionary.TryAdd(coordinate.y, 0);

                    breakColumnDictionary[coordinate.x]++;
                    breakRowDictionary[coordinate.y]++;
                }

                var scorePosition = CalculateRectFromPoints(_willBreakElementInBlockPositions)
                    .position;
                _score.HandlePlacedMatchableBlock(_brokenLines, scorePosition, block.TypeData.type);
                
                AudioShortcut.PlayBreakBlock(_score.CurrentCombo);
                
                foreach (var pair in breakColumnDictionary)
                {
                    if (pair.Value <= 9) continue;
                    var breakingPrefab = _breakingEffectBrickStyleConfigs.GetPrefab(typeData.type);
                    var breakingFxTransform = Core.ScenePool.Spawn(breakingPrefab, default, default).transform;
                    var newPosition = _board.GetWorldPositionByGridIndex(pair.Key, Board.GridNumber / 2);
                    newPosition.y -= Board.BlockSize / 2f;
                    breakingFxTransform.position = newPosition;
                    breakingFxTransform.eulerAngles = new Vector3(0f, 0f, 90f);
                }

                foreach (var pair in breakRowDictionary)
                {
                    if (pair.Value <= 9) continue;
                    var breakingPrefab = _breakingEffectBrickStyleConfigs.GetPrefab(typeData.type);
                    var breakingFxTransform = Core.ScenePool.Spawn(breakingPrefab, default, default).transform;
                    var newPosition = _board.GetWorldPositionByGridIndex(Board.GridNumber / 2, pair.Key);
                    newPosition.x -= Board.BlockSize / 2f;
                    breakingFxTransform.position = newPosition;
                    breakingFxTransform.eulerAngles = new Vector3(0f, 0f, 0f);
                }

                yield return new WaitForSeconds(0.2f);
            }
            else
            {
                _score.HandlePlacedUnMatchableBlock();
            }

            yield return new WaitForSeconds(0.1f);

            _willBreakElementCoordinates.Clear();
            localCheckingElements.Clear();
            gotStarElements.Clear();
            _brokenLines = 0;
            _board.ResetBreakingOrderForAllElements();

            var anyBlockLeft = _spawner.AnyBlockLeft();

            if (!anyBlockLeft)
            {
                if (_tutorial.pTutorialQueueCount <= 0)
                {
                    _spawner.SpawnAllBlocks();
                    _spawner.ShowBlocks();
                }
            }

            HandleTutorial();

            var availableSpaceForBlocks = CheckAvailableSpaceForShape();
#if UNITY_EDITOR
            // availableSpaceForBlocks = false;
#endif

            if (availableSpaceForBlocks)
            {
                _placingBlock = false;
            }
            else
            {
                _input.EnableTouch(false);

                LoseGame();
            }
        }

        [Button(ButtonSizes.Gigantic)]
        private void GameOver()
        {
            UIShortcut.ShowPopup(UIKeys.Panel.POPUP_OUT_OF_SPACE, PanelArgs.Popup.AddData("score", _score.CurrentScore));
        }

        [Button(ButtonSizes.Gigantic)]
        public bool CheckAvailableSpaceForBlocks()
        {
            var available = false;
            for (var i = 0; i < 3; i++)
            {
                var block = _spawner.GetBlock(i);
                if (block == null) continue;
                if (_board.CheckIfAvailableForBlock(block.RelativeGridCoordinates))
                {
                    available = true;
                    block.Activate();
                }
                else
                {
                    block.Deactivate();
                }
            }

            return available;
        }

        [Button(ButtonSizes.Gigantic)]
        public bool CheckAvailableSpaceForShape()
        {
            var available = false;
            for (var i = 0; i < 3; i++)
            {
                var block = _spawner.GetBlock(i);
                if (block == null) continue;
                if (_board.CheckIfAvailableForBlock(block.RelativeGridCoordinates))
                {
                    available = true;
                    block.Activate();
                }
                else
                {
                    block.Deactivate();
                }
            }

            return available;
        }

        private void HighlightCheckingElements(GridElementTypeData typeData)
        {
            foreach (var t in _checkingElements)
                t.Highlight(typeData);
        }

        private void UnhighlightCheckingElements()
        {
            foreach (var t in _checkingElements)
                t.Unhighlight();
        }

        private List<GridElement> GetGridCellGotStarByBlock(Block block)
        {
            var cells = new List<GridElement>();
            var starWorldPositions = block.starWorldPositions;
            foreach (var t in starWorldPositions)
            {
                if (_board.TryGetElementByWorldPosition(t, out var cell))
                    cells.Add(cell);
            }

            return cells;
        }

        #endregion

        #region Tutorial

        public bool DidShowTutorial()
        {
            return _tutorial.DidShowTutorial();
        }

        private int _tutorialStep;

        private void HandleTutorial()
        {
            if (DataShortcut.BlockPuzzle.ShownTutorial)
                return;

            if (_tutorialStep == 0)
                _score.Reset();

            _currentTutorialData = _tutorial.GetTutorial();
            _tutorialStep++;
            if (_currentTutorialData != null)
            {
                var boardDto = JsonConvert.DeserializeObject<BoardSaveModelDto>(_currentTutorialData.boardDataJson);
                _board.LoadBoardDataFromDto(boardDto);

                var blockDto = JsonConvert.DeserializeObject<BlockSaveModelDto>(_currentTutorialData.blockDataJson);
                _spawner.LoadBlocksFromDto(blockDto);

                _spawner.ShowBlocks();

                _checkingElements.Clear();
                _willBreakElementCoordinates.Clear();

                var targetUnitRect = GetTutorialTargetUnitRect();
                var worldPos = targetUnitRect.position;
                _tutorial.Show(worldPos, UnitToPixel(targetUnitRect.width), UnitToPixel(targetUnitRect.height));

                var block = _spawner.GetBlock(1);
                if (block != null)
                {
                    ShowTutorialAnimation(block);
                }
            }
            else
            {
                _tutorial.Hide();
                DataShortcut.BlockPuzzle.ShownTutorial = true;
            }
        }

        private void ShowTutorialAnimation(Block block)
        {
            bool blockFit = _board.CheckIfBlockFitSlots(block, _currentTutorialData.targetPositions);
            if (blockFit)
                block.Activate();
            else
                block.Deactivate();

            if (!blockFit)
            {
                _tutorial.AnimatePressingHand(_spawner.SpawningPositions[1].position);
            }
            else
            {
                _tutorial.AnimateMovingHand(_spawner.GetTutorialBlockWorldPosition());
            }
        }

        private Rect GetTutorialTargetUnitRect()
        {
            Vector2 offset = new Vector2(0.5f, 0.5f);
            GridCoordinate firstCoordinate = _currentTutorialData.targetPositions[0];
            GridElement firstElement = _board.GetElement(firstCoordinate.x, firstCoordinate.y);
            Vector2 bottomLeftPoint = (Vector2)firstElement.VTransform.position - offset;
            Vector2 topRightPoint = (Vector2)firstElement.VTransform.position + offset;
            for (int i = 0; i < _currentTutorialData.targetPositions.Count; i++)
            {
                GridCoordinate coord = _currentTutorialData.targetPositions[i];
                Vector2 localPos = _board.GetElement(coord.x, coord.y).VTransform.position;
                if (localPos.x - 0.5f < bottomLeftPoint.x)
                    bottomLeftPoint.x = localPos.x - 0.5f;

                if (localPos.y - 0.5f < bottomLeftPoint.y)
                    bottomLeftPoint.y = localPos.y - 0.5f;

                if (localPos.x + 0.5f > topRightPoint.x)
                    topRightPoint.x = localPos.x + 0.5f;

                if (localPos.y + 0.5f > topRightPoint.y)
                    topRightPoint.y = localPos.y + 0.5f;
            }

            Vector2 centerPos = (topRightPoint + bottomLeftPoint) / 2f;
            Vector2 size = new Vector2(topRightPoint.x - bottomLeftPoint.x, topRightPoint.y - bottomLeftPoint.y);
            return new Rect(centerPos, size);
        }

        private float UnitToPixel(float unit)
        {
            if (Camera.main != null)
                return unit * (720f / Camera.main.aspect * 0.5f) / Camera.main.orthographicSize;
            return 0;
        }

        private Rect CalculateRectFromPoints(List<Vector2> positions)
        {
            if (positions.Count == 0)
                return new Rect(Vector2.zero, Vector2.zero);

            Vector2 bottomLeftPoint = positions[0];
            Vector2 topRightPoint = positions[0];
            for (int i = 0; i < positions.Count; i++)
            {
                Vector2 localPos = positions[i];
                if (localPos.x < bottomLeftPoint.x)
                    bottomLeftPoint.x = localPos.x;

                if (localPos.y < bottomLeftPoint.y)
                    bottomLeftPoint.y = localPos.y;

                if (localPos.x > topRightPoint.x)
                    topRightPoint.x = localPos.x;

                if (localPos.y > topRightPoint.y)
                    topRightPoint.y = localPos.y;
            }

            Vector2 centerPos = (topRightPoint + bottomLeftPoint) / 2f;
            Vector2 size = new Vector2(topRightPoint.x - bottomLeftPoint.x, topRightPoint.y - bottomLeftPoint.y);
            return new Rect(centerPos, size);
        }
        
        #region Tutorial Editor

        public void CleanBoard()
        {
            _board.ResetAllElements();
        }

        #endregion

        #endregion
    }
}