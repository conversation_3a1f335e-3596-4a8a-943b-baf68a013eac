using UnityEngine;
using System.Collections;
using PrimeTween;

namespace OP.BlockPuzzle
{
    public class BreakBlockAnimation : MonoBehaviour
    {
        [SerializeField] SpriteRenderer m_Renderer;

        Transform _transform;

        public Transform VTransform
        {
            get
            {
                if (_transform == null)
                    _transform = transform;

                return _transform;
            }
        }

        public void Break(Sprite sprite)
        {
            m_Renderer.sprite = sprite;

            StartCoroutine(YieldBreak());
        }

        private IEnumerator YieldBreak()
        {
            VTransform.rotation = default(Quaternion);
            float currentScale = VTransform.localScale.x;

            float randomTargetRotateZ = Random.Range(-270, 270);
            float randomTargetPositionX = Random.Range(-4f, 4f);
            float randomHeightY = Random.Range(2f, 3f);

            Sequence breakSequence = Sequence.Create();
            breakSequence.Insert(0, Tween.Scale(VTransform, currentScale * 1.1f, 0.2f));
            breakSequence.Insert(0, Tween.LocalRotation(VTransform, new Vector3(0f, 0f, randomTargetRotateZ), 1.2f));
            breakSequence.Insert(0, Tween.PositionY(VTransform, randomHeightY + VTransform.position.y, 0.4f, Ease.OutCubic));
            breakSequence.Insert(0.4f, Tween.PositionY(VTransform, -15f + VTransform.position.y, 0.8f, Ease.InQuad));
            breakSequence.Insert(0, Tween.PositionX(VTransform, randomTargetPositionX + VTransform.position.x, 1.2f, Ease.Linear));

            yield return breakSequence;

            VTransform.localScale = Vector3.one;
            Core.ScenePool.Recycle(VTransform.gameObject);
        }

        public void BreakByBomb(Vector3 explodingPoint, Sprite sprite)
        {
            m_Renderer.sprite = sprite;

            StartCoroutine(YieldBreakByBomb(explodingPoint));
        }

        private IEnumerator YieldBreakByBomb(Vector3 explodingPoint)
        {
            VTransform.rotation = default(Quaternion);
            float currentScale = VTransform.localScale.x;
            var direction = (VTransform.position - explodingPoint).normalized * 15;

            float randomTargetRotateZ = Random.Range(-270, 270);
            float randomTargetPositionX = Random.Range(-1f, 1f) + direction.x;
            float randomHeightY = Random.Range(2f, 3f) + direction.y;

            Sequence breakSequence = Sequence.Create();
            breakSequence.Insert(0, Tween.Scale(VTransform, currentScale * 1.1f, 0.2f));
            breakSequence.Insert(0, Tween.LocalRotation(VTransform, new Vector3(0f, 0f, randomTargetRotateZ), 1.2f));
            breakSequence.Insert(0, Tween.PositionY(VTransform, randomHeightY + VTransform.position.y, 0.8f, Ease.OutCubic));
            breakSequence.Insert(0, Tween.PositionX(VTransform, randomTargetPositionX + VTransform.position.x, 1.0f, Ease.Linear));

            yield return breakSequence;

            VTransform.localScale = Vector3.one;
            Core.ScenePool.Recycle(VTransform.gameObject);
        }
    }
}