
using System.Collections.Generic;
using OP.BlockPuzzle;

public static class BlockExtensions
{
    public static BlockDataDto ToDto(this Block block)
    {
        var dto = new BlockDataDto
        {
            numberId = block.Id,
            type = block.TypeData != null ? (int)block.TypeData.type : -1,
            blockAngle = (int)block.BlockAngle,
            starCoords = new List<GridCoordinateDto>()
        };

        foreach (var coord in block.RelativeGridCoordinates)
        {
            if (block.StarCoordinates.Exists(star => star.Compare(coord)))
            {
                dto.starCoords.Add(new GridCoordinateDto
                {
                    coordX = coord.x,
                    coordY = coord.y
                });
            }
        }

        return dto;
    }
}
