using System;
using Sirenix.OdinInspector;
using UnityEngine;

namespace OP.BlockPuzzle
{
    public class BlockElement : BaseBehaviour
    {
        public SpriteRenderer mainRenderer;
        public SpriteRenderer shadowRenderer;
        public SpriteRenderer itemInsideRenderer;
        public string dataElementString;

        private void OnEnable()
        {
            itemInsideRenderer.enabled = false;
        }

        public bool pEnableShadow
        {
            get => shadowRenderer.enabled;
            set => shadowRenderer.enabled = value;
        }

        public void SetItemInside(Sprite sprite)
        {
            itemInsideRenderer.enabled = true;
            itemInsideRenderer.sprite = sprite;
            itemInsideRenderer.sortingOrder = mainRenderer.sortingOrder + 1;
        }
    }
}