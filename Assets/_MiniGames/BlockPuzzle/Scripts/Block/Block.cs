using System;
using System.Collections.Generic;
using System.Linq;
using Sirenix.OdinInspector;
using UnityEditor;
using UnityEngine;
using PrimeTween;

namespace OP.BlockPuzzle
{
    public enum BlockAngle
    {
        Angle0 = 0,
        Angle90 = 90,
        Angle180 = 180,
        Angle270 = 270
    }

    public class Block : BaseBehaviour
    {
        #region Events

        private Action _onPlacedBlock;

        #endregion

        #region Properties

        public List<BlockElement> elements;

        public int Id { get; private set; } = -1;

        [ShowInInspector] public BlockAngle BlockAngle { get; private set; }

        private BlockAngle _mOriginalAngle;

        [SerializeField] private List<GridCoordinate> mRelativeGridCoordinates;

        public List<GridCoordinate> RelativeGridCoordinates => mRelativeGridCoordinates;

        public GridElementTypeData TypeData { get; private set; }

        private Rect _mBlockRect;

        private Vector3 _mOriginalPosition;

        private bool _mHandMoving;
        private bool _mShouldMove;
        private bool _mHandReleased;
        private bool _mShouldMoveBack;
        private bool _shouldBePlaced;
        private Vector3 _mLastTargetPosition;
        private Vector3 _targetPosition;

        private bool _mIsRotateMode;

        private int _randomBoxCount;

        public List<Vector3> starWorldPositions
        {
            get
            {
                List<Vector3> positions = new List<Vector3>();
                for (int i = 0; i < m_StarCoordinates.Count; i++)
                {
                    GridCoordinate coordinate = m_StarCoordinates[i];
                    Vector3 localPosition = elements[0].VTransform.localPosition +
                                            new Vector3(coordinate.x, coordinate.y, 0);
                    positions.Add(VTransform.TransformPoint(localPosition));
                }

                return positions;
            }
        }

        private List<GridCoordinate> m_StarCoordinates;
        public List<GridCoordinate> StarCoordinates => m_StarCoordinates;

        #endregion

        #region Unity Events

        private void LateUpdate()
        {
            if (!_mShouldMove)
                return;

            var speed = 150f;
            if (_mHandReleased)
            {
                if (_shouldBePlaced)
                    speed = 4f;
                else if (_mShouldMoveBack)
                    speed = 60f;
                else
                    speed = 3f;
            }
            else if (!_mHandMoving)
                speed = 60f;

            var delta = speed * Time.deltaTime;
            var sqrDistance = Vector3.SqrMagnitude(_targetPosition - VTransform.position);

            VTransform.position = Vector3.MoveTowards(VTransform.position, _targetPosition, delta);

            if (sqrDistance <= 0.4f && _mShouldMoveBack)
            {
                VTransform.position = _targetPosition;
                _mShouldMove = false;
                _mShouldMoveBack = false;
            }

            if (sqrDistance <= 0.05f && _shouldBePlaced)
            {
                _shouldBePlaced = false;
                _onPlacedBlock?.Invoke();
            }
        }

        #endregion

        #region Methods

        private readonly List<Vector3> m_OriginalChildrenLocalPositions = new List<Vector3>();

        public void Configure(int id, BlockAngle targetAngle, List<GridCoordinate> starCoords)
        {
            Id = id;

            elements = new List<BlockElement>();
            mRelativeGridCoordinates = new List<GridCoordinate>();
            m_StarCoordinates = new List<GridCoordinate>(starCoords);

            bool localPositionArrayInit = m_OriginalChildrenLocalPositions.Count > 0;

            Transform cacheTrans = transform;
            for (int i = 0; i < cacheTrans.childCount; i++)
            {
                Transform child = cacheTrans.GetChild(i);
                BlockElement element = child.GetComponent<BlockElement>();
                if (element == null)
                    continue;

                element.name = $"{name} - Element {i}";

                if (!localPositionArrayInit)
                    m_OriginalChildrenLocalPositions.Add(child.localPosition * Board.BlockSize);

                child.localScale = Vector3.one;
                child.localPosition = GetChildLocalPositionByAngle(BlockAngle.Angle0, targetAngle,
                    m_OriginalChildrenLocalPositions[i]);

                elements.Add(element);


                element.mainRenderer.sortingOrder = 15;

                Vector2 offset = child.localPosition - elements[0].VTransform.localPosition;
                GridCoordinate coordinate = new GridCoordinate((int)offset.x, (int)offset.y);
                mRelativeGridCoordinates.Add(coordinate);
            }

            BlockAngle = targetAngle;

            CalculateBlockRect();
            EnableShadow(true);
        }

        public void ConfigureShape(int id, BlockAngle targetAngle)
        {
            Id = id;

            elements = new List<BlockElement>();
            mRelativeGridCoordinates = new List<GridCoordinate>();

            var localPositionArrayInit = m_OriginalChildrenLocalPositions.Count > 0;

            var cacheTrans = transform;
            for (var i = 0; i < cacheTrans.childCount; i++)
            {
                var child = cacheTrans.GetChild(i);
                var element = child.GetComponent<BlockElement>();
                if (element == null)
                    continue;

                element.name = $"{name} - Element {i}";

                if (!localPositionArrayInit)
                    m_OriginalChildrenLocalPositions.Add(child.localPosition * Board.BlockSize);

                child.localScale = Vector3.one * 1.25f;
                child.localPosition = GetShapeChildLocalPosition(BlockAngle.Angle0, targetAngle,
                    m_OriginalChildrenLocalPositions[i]);

                elements.Add(element);


                element.mainRenderer.sortingOrder = 15;

                Vector2 offset = child.localPosition - elements[0].VTransform.localPosition;
                var coordinate = new GridCoordinate((int)offset.x, (int)offset.y);
                mRelativeGridCoordinates.Add(coordinate);
            }

            BlockAngle = targetAngle;

            CalculateBlockRect();
            EnableShadow(true);
        }

        public void ConfigureShapeStarCoordinate(List<GridCoordinate> starCoords)
        {
            starCoords = starCoords.OrderBy(x => x.GetHashCode()).ToList();
            m_StarCoordinates = new List<GridCoordinate>(starCoords.Take(1));
        }

        public void ConfigureOriginalPosition(Vector3 localPos)
        {
            VTransform.localPosition = localPos;
            _mOriginalPosition = VTransform.position;
        }

        public void ConfigureType(GridElementTypeData typeData)
        {
            TypeData = typeData;


            for (var i = 0; i < elements.Count; i++)
            {
                var containStar = false;
                for (var j = 0; j < m_StarCoordinates.Count; j++)
                {
                    if (m_StarCoordinates[j].Compare(mRelativeGridCoordinates[i]))
                    {
                        containStar = true;
                    }
                }

                elements[i].mainRenderer.sprite = containStar ? TypeData.iconWithStar : TypeData.icon;
                elements[i].mainRenderer.color = Color.white;
            }
        }

        private void CalculateBlockRect()
        {
            if (elements.Count == 0)
                return;

            Vector2 bottomLeftPoint = elements[0].VTransform.localPosition;
            Vector2 topRightPoint = elements[0].VTransform.localPosition;
            for (int i = 0; i < elements.Count; i++)
            {
                Vector2 localPos = elements[i].VTransform.localPosition;
                if (localPos.x < bottomLeftPoint.x)
                    bottomLeftPoint.x = localPos.x;

                if (localPos.y < bottomLeftPoint.y)
                    bottomLeftPoint.y = localPos.y;

                if (localPos.x > topRightPoint.x)
                    topRightPoint.x = localPos.x;

                if (localPos.y > topRightPoint.y)
                    topRightPoint.y = localPos.y;
            }

            Vector2 centerPos = (topRightPoint + bottomLeftPoint) / 2f;
            Vector2 size = new Vector2(topRightPoint.x - bottomLeftPoint.x, topRightPoint.y - bottomLeftPoint.y);
            _mBlockRect = new Rect(centerPos, size);
        }

        public void CalculateChildrenPositions()
        {
            for (int i = 0; i < elements.Count; i++)
            {
                elements[i].VTransform.localPosition -= (Vector3)_mBlockRect.position;
            }
        }

        [Button("Rotate 90 degree")]
        public void Rotate90Degree()
        {
            var newAngle = (BlockAngle)(((int)BlockAngle + 90) % 360);
            Rotate(newAngle, true);
        }

        private void Rotate(BlockAngle angle, bool withAnimation)
        {
            var containStarArray = new bool[mRelativeGridCoordinates.Count];
            for (var i = 0; i < mRelativeGridCoordinates.Count; i++)
            {
                var containStar = false;
                for (var j = 0; j < m_StarCoordinates.Count; j++)
                {
                    if (!mRelativeGridCoordinates[i].Compare(m_StarCoordinates[j])) continue;
                    containStar = true;
                    break;
                }

                containStarArray[i] = containStar;
            }


            mRelativeGridCoordinates.Clear();
            m_StarCoordinates.Clear();
            for (int i = 0; i < elements.Count; i++)
            {
                Transform child = elements[i].VTransform;

                child.localPosition = GetChildLocalPositionByAngle(BlockAngle, angle, child.localPosition);

                Vector2 offset = child.localPosition - elements[0].VTransform.localPosition;
                GridCoordinate coordinate = new GridCoordinate((int)offset.x, (int)offset.y);
                mRelativeGridCoordinates.Add(coordinate);

                if (containStarArray[i])
                    m_StarCoordinates.Add(coordinate);
            }

            BlockAngle = angle;

            CalculateBlockRect();

            if (withAnimation)
            {
                VTransform.eulerAngles = new Vector3(0, 0, 90);
                Tween.LocalRotation(VTransform, Vector3.zero, 0.2f);
            }

            Core.Manager_BlockPuzzle.CheckAvailableSpaceForBlocks();
        }

        public void TurnRotateMode(bool enable)
        {
            _mIsRotateMode = enable;

            if (enable)
            {
                _mOriginalAngle = BlockAngle;
            }
        }

        public void RevertOriginalRotation()
        {
            Rotate(_mOriginalAngle, false);
        }

        public void HandleSelected(Vector3 worldPos)
        {
            _mShouldMoveBack = false;
            _mHandMoving = false;
            _mHandReleased = false;
            _shouldBePlaced = false;

            _targetPosition = worldPos;
            _targetPosition.y += 3.5f;
            _mLastTargetPosition = _targetPosition;

            if (!_mIsRotateMode)
            {
                _mShouldMove = true;

                ScaleWithChildren(1, Board.BlockSize * 0.9f, 0.2f);

                for (int i = 0; i < elements.Count; i++)
                {
                    bool containStar = false;
                    for (int j = 0; j < m_StarCoordinates.Count; j++)
                    {
                        if (m_StarCoordinates[j].Compare(mRelativeGridCoordinates[i]))
                        {
                            containStar = true;
                        }
                    }

                    elements[i].mainRenderer.sprite = containStar ? TypeData.iconWithStar : TypeData.icon;

                    elements[i].mainRenderer.sortingOrder = 36;
                    elements[i].itemInsideRenderer.sortingOrder = 37;

                    EnableShadow(false);
                }
            }
        }

        public void HandleMoving(Vector3 worldPos)
        {
            _targetPosition = worldPos;
            _targetPosition.y += 3.5f;

            if (Vector3.Distance(_targetPosition, _mLastTargetPosition) < 0.2f && !_mHandMoving)
            {
                if (!_mIsRotateMode)
                {
                    _mShouldMove = true;
                }
            }
            else
            {
                if (!_mHandMoving)
                    ScaleWithChildren(1, Board.BlockSize * 0.85f, 0.05f);

                _mHandMoving = true;

                _mShouldMove = true;
            }
        }

        public void HandleRelease()
        {
            _mHandReleased = true;

            if (!_mIsRotateMode || _mHandMoving)
            {
                for (int i = 0; i < elements.Count; i++)
                {
                    elements[i].mainRenderer.sortingOrder = 15;
                }
            }

            if (!_mHandMoving && _mIsRotateMode)
            {
                Rotate90Degree();
            }
        }

        public void MoveBack()
        {
            _mShouldMoveBack = true;
            _targetPosition = _mOriginalPosition;

            UnhighlightAllElements();

            ScaleWithChildren(0.5f, Board.BlockSize * 1, 0.2f);

            EnableShadow(true);
        }

        public void UnhighlightAllElements()
        {
            for (int i = 0; i < elements.Count; i++)
            {
                bool containStar = false;
                for (int j = 0; j < m_StarCoordinates.Count; j++)
                {
                    if (m_StarCoordinates[j].Compare(mRelativeGridCoordinates[i]))
                    {
                        containStar = true;
                    }
                }

                elements[i].mainRenderer.sprite = containStar ? TypeData.iconWithStar : TypeData.icon;
            }
        }

        public void PlaceBlock(Vector3 targetPos, Action onPlacedBlock)
        {
            _targetPosition = targetPos;
            _shouldBePlaced = true;
            _onPlacedBlock = onPlacedBlock;
        }

        public void ScaleWithChildren(float blockScale, float childScale, float duration,
            Action onComplete = null)
        {
            for (var i = 0; i < elements.Count; i++)
            {
                if (Mathf.Approximately(elements[i].VTransform.localScale.x, childScale)) continue;
                
                Tween.Scale(elements[i].VTransform, childScale, duration);
            }

            // if (Mathf.Approximately(VTransform.localScale.x, blockScale)) return;
            Tween.Scale(VTransform, blockScale, duration).OnComplete(() => { onComplete?.Invoke(); });
        }

        public void Activate()
        {
            for (int i = 0; i < elements.Count; i++)
            {
                Tween.Color(elements[i].mainRenderer, Color.white, 0.5f);
            }
        }

        public void Deactivate()
        {
            for (int i = 0; i < elements.Count; i++)
            {
                Tween.Color(elements[i].mainRenderer, new Color(0.7f, 0.7f, 0.7f, 1f), 0.5f);
            }
        }

        public void Highlight(bool highlight)
        {
            if (TypeData == null)
                return;

            for (int i = 0; i < elements.Count; i++)
            {
                bool containStar = false;
                for (int j = 0; j < m_StarCoordinates.Count; j++)
                {
                    if (m_StarCoordinates[j].Compare(mRelativeGridCoordinates[i]))
                    {
                        containStar = true;
                    }
                }

                elements[i].mainRenderer.sprite = containStar
                    ? highlight ? TypeData.iconWithStarHighlight : TypeData.iconWithStar
                    : highlight
                        ? TypeData.iconHighlight
                        : TypeData.icon;
            }
        }

        public void HighlightSingle(int index, bool highlight)
        {
            if (index < 0 || index >= elements.Count)
                return;

            bool containStar = false;
            for (int j = 0; j < m_StarCoordinates.Count; j++)
            {
                if (m_StarCoordinates[j].Compare(mRelativeGridCoordinates[index]))
                {
                    containStar = true;
                }
            }

            elements[index].mainRenderer.sprite = containStar
                ? highlight ? TypeData.iconWithStarHighlight : TypeData.iconWithStar
                : highlight
                    ? TypeData.iconHighlight
                    : TypeData.icon;
        }

        public void Break()
        {
            var breakingPrefab = Core.Manager_BlockPuzzle.BreakingEffectPrefab.transform;
            for (var i = 0; i < elements.Count; i++)
            {
                var breakAnimation = Core.ScenePool.Spawn(breakingPrefab.gameObject, default, default)
                    .GetComponent<BreakBlockAnimation>();
                breakAnimation.VTransform.localScale = new Vector3(0.5f, 0.5f, 1f);
                breakAnimation.VTransform.position = elements[i].VTransform.position;
                breakAnimation.Break(TypeData.icon);
            }
        }

        private Vector2 GetChildLocalPositionByAngle(BlockAngle originalAngle, BlockAngle newAngle,
            Vector2 originalLocalPos)
        {
            int changeAngle = (int)newAngle - (int)originalAngle;
            changeAngle = (changeAngle < 0) ? 360 + changeAngle : changeAngle;

            return changeAngle switch
            {
                90 => new Vector2(originalLocalPos.y, -1 * originalLocalPos.x),
                180 => new Vector2(originalLocalPos.x * -1, originalLocalPos.y * -1),
                270 => new Vector2(originalLocalPos.y * -1, originalLocalPos.x),
                _ => originalLocalPos
            };
        }

        private Vector2 GetShapeChildLocalPosition(BlockAngle originalAngle, BlockAngle newAngle,
            Vector2 originalLocalPos)
        {
            int changeAngle = (int)newAngle - (int)originalAngle;
            changeAngle = changeAngle < 0 ? 360 + changeAngle : changeAngle;

            return changeAngle switch
            {
                90 => new Vector2(originalLocalPos.y, -1 * originalLocalPos.x),
                180 => new Vector2(originalLocalPos.x * -1, originalLocalPos.y * -1),
                270 => new Vector2(originalLocalPos.y * -1, originalLocalPos.x),
                _ => originalLocalPos
            };
        }

        private void EnableShadow(bool enabled)
        {
            for (int i = 0; i < elements.Count; i++)
            {
                elements[i].pEnableShadow = enabled;
            }
        }

        public void Reset()
        {
            _mShouldMove = false;
            _mShouldMoveBack = false;
            _mIsRotateMode = false;
        }

        #endregion

        #region Configures

#if UNITY_EDITOR
        [Button("Setup Block Children")]
        public void SetupBlockChildren()
        {
            Configure(1, BlockAngle.Angle0, new List<GridCoordinate>());
            CalculateChildrenPositions();
        }

        [Button("Setup Elements")]
        public void SetupElements()
        {
            var elementPrefab =
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/_MAIN/Prefabs/BlockElement.prefab");
            List<Vector3> elementPositions = new List<Vector3>();

            for (int i = 0; i < transform.childCount; i++)
            {
                var child = transform.GetChild(i);
                elementPositions.Add(child.localPosition);

                Debug.Log($"Removing child {child.name} with pos {child.localPosition}");
            }

            // Remove old SpriteRenderer objects
            for (int i = transform.childCount - 1; i >= 0; i--)
            {
                var child = transform.GetChild(i);
                DestroyImmediate(child.gameObject);
            }

            var elementCount = 0;
            foreach (var position in elementPositions)
            {
                // Spawn new element
                var elementObject =
                    PrefabUtility.InstantiatePrefab(elementPrefab) as GameObject;
                if (elementObject == null) continue;
                elementObject.transform.SetParent(transform);
                elementObject.transform.localPosition = position;
                elementObject.name = $"{name} - Element {elementCount}";
                elementCount++;
                Debug.Log($"Adding child {elementObject.name} with pos {position}");
            }

            EditorUtility.SetDirty(this);
        }
#endif

        #endregion
    }
}