using System;
using System.Collections;
using System.Collections.Generic;
using Sirenix.OdinInspector;
using TMPro;
using UnityEngine;
using Color = UnityEngine.Color;
using Random = UnityEngine.Random;
using PrimeTween;

namespace OP.BlockPuzzle
{
    [Serializable]
    public struct GridCoordinate : IEquatable<GridCoordinate>
    {
        public GridCoordinate(int x, int y)
        {
            this.x = x;
            this.y = y;
        }

        public bool Compare(GridCoordinate other)
        {
            return x == other.x && y == other.y;
        }

        public int x;
        public int y;

        public bool Equals(GridCoordinate other)
        {
            return x == other.x && y == other.y;
        }

        public override bool Equals(object obj)
        {
            return obj is GridCoordinate other && Equals(other);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(x, y);
        }
    }

    public enum GridElementState
    {
        NORMAL,
        EMPTY_HIGHLIGHT,
        FILLED_HIGHLIGHT,
        FILLED
    }

    public enum BrokenElementState
    {
        NONE = 0,
        MAIN_BROKEN_ELEMENT = 1,
        RELATIVE_BROKEN_ELEMENT = 2
    }

    public enum BrokenElementStyle
    {
        Explosion,
        Woody,
        Blast,
        Jewel
    }

    public class GridElement : BaseBehaviour
    {
        #region Constants

        private static readonly Color WhiteColor = new(1, 1, 1, 1);
        private static readonly Color TransparentColor = new(1, 1, 1, 0);
        private static readonly Color HalfTransparentColor = new(1, 1, 1, 0.5f);
        private static readonly Color BreakAnimationStartColor = new(1, 1, 1, 0.0f);
        private const float FallAppleDuration = 0.15f;

        #endregion

        #region Fields

        [SerializeField] private string dataString;

        [TabGroup("Type Data")] public GridElementTypeData typeData;

        [SerializeField, TabGroup("Sprite Renderer")]
        private SpriteRenderer m_ForegroundRenderer;

        [SerializeField, TabGroup("Sprite Renderer")]
        private SpriteRenderer m_BackgroundRenderer;

        [SerializeField, TabGroup("Sprite Renderer")]
        private SpriteRenderer m_GrayRenderer;

        [SerializeField, TabGroup("Effect")] private GameObject m_BreakingEffectPrefab;

        [SerializeField, TabGroup("Effect")] private GameObject m_BreakingEffectWoodyStylePrefab;
        [SerializeField, ReadOnly] private List<GridElement> listElementsBelow;

        #endregion

        #region Properties

        [ShowInInspector] public GridCoordinate coordinate;
        public GridElementState state;
        public BrokenElementState brokenState = BrokenElementState.NONE;

        public int breakingOrder;
        public int breakingOrderWoody;

        private Transform m_ForegroundTransform;

        private Coroutine _breakCoroutine;

        public bool containStar { get; protected set; }

        #endregion


        #region Methods

        public void Configure(GridCoordinate newCoordinate)
        {
            coordinate = newCoordinate;
            breakingOrder = -1;
            breakingOrderWoody = -1;
            brokenState = BrokenElementState.NONE;
            m_ForegroundTransform = m_ForegroundRenderer.transform;

            ResetBreakingOrder();
            ClearData();
            ResetRender();
        }

        public void SetTextData(string data)
        {
            dataString = data;
        }

        public string GetTextData()
        {
            return dataString;
        }

        public void Highlight(GridElementTypeData data)
        {
            if (typeData != null)
            {
                HighlightFilled(data);
            }
            else
            {
                HighlightEmpty(data);
            }
        }

        private void HighlightFilled(GridElementTypeData data)
        {
            state = GridElementState.FILLED_HIGHLIGHT;
            m_ForegroundRenderer.color = WhiteColor;

            m_ForegroundRenderer.sprite = GetClassicFilledSprite(data);
        }

        private void HighlightEmpty(GridElementTypeData data)
        {
            state = GridElementState.EMPTY_HIGHLIGHT;
            m_ForegroundRenderer.color = HalfTransparentColor;

            m_ForegroundRenderer.sprite = (containStar ? data.iconWithStar : data.icon);
        }

        private Sprite GetClassicFilledSprite(GridElementTypeData data)
        {
            return containStar ? data.iconWithStarHighlight : data.iconHighlight;
        }

        public void Unhighlight()
        {
            switch (state)
            {
                case GridElementState.NORMAL:
                    return;
                case GridElementState.EMPTY_HIGHLIGHT:
                    m_ForegroundRenderer.sprite = null;
                    break;
                case GridElementState.FILLED_HIGHLIGHT:
                    m_ForegroundRenderer.sprite = typeData.icon;
                    break;
            }

            m_ForegroundRenderer.color = WhiteColor;
            // state = GridElementState.NORMAL;
        }


        public void FillWithData(GridElementTypeData data, bool hasStar = false, bool willBreakSoon = false)
        {
            if (_breakCoroutine != null)
                StopCoroutine(_breakCoroutine);

            typeData = data;
            containStar = hasStar;
            m_ForegroundRenderer.sprite = willBreakSoon
                ? typeData.iconHighlight
                : hasStar
                    ? typeData.iconWithStar
                    : typeData.icon;
            m_ForegroundRenderer.color = WhiteColor;
            state = GridElementState.FILLED;
        }

        public void FillStar()
        {
            containStar = true;
            m_ForegroundRenderer.sprite = typeData?.iconWithStar;
        }

        public void Break(GridElementTypeData elementTypeData, int brokenLines)
        {
            ApplyBrokenStyle(elementTypeData, brokenLines);
            ClearData();
        }

        private void ApplyBrokenStyle(GridElementTypeData data, int brokenLines)
        {
            var style = Core.Definition.BlockPuzzle.breakBlockStyle;
            switch (style)
            {
                case BrokenElementStyle.Explosion:
                    BreakWithExplodeStyle(data);
                    break;
                case BrokenElementStyle.Woody:
                    _breakCoroutine = StartCoroutine(YieldBreakWoodyStyle(brokenLines));
                    break;
                case BrokenElementStyle.Blast:
                case BrokenElementStyle.Jewel:
                    ResetRender();
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

        // ReSharper disable Unity.PerformanceAnalysis
        private void BreakWithExplodeStyle(GridElementTypeData elementTypeData)
        {
            var breakAnimation = Core.ScenePool.Spawn(m_BreakingEffectPrefab, default, default)
                .GetComponent<BreakBlockAnimation>();
            breakAnimation.VTransform.position = VTransform.position;
            breakAnimation.Break(elementTypeData.icon);

            ResetRender();
        }

        // ReSharper disable Unity.PerformanceAnalysis
        private IEnumerator YieldBreakWoodyStyle(int brokenLines)
        {
            const float duration = 0.68f;
            const float delayPerAnimation = duration / 34f * 3f;
            yield return new WaitForSeconds(breakingOrderWoody * delayPerAnimation);

            ResetRender();

            var breakAnimation = Core.ScenePool.Spawn(m_BreakingEffectWoodyStylePrefab, default, default)
                .GetComponent<GridElementBreakingFXWoodyStyle>();

            breakAnimation.pTransform.position = VTransform.position;
            breakAnimation.pTransform.localScale = Vector3.one;
            breakAnimation.pRenderer.color = BreakAnimationStartColor;
            breakAnimation.Init(breakingOrderWoody, brokenLines);
            breakAnimation.Break(duration);
        }

        // ReSharper disable Unity.PerformanceAnalysis
        public void BreakByBomb(Vector3 explodingPoint)
        {
            var breakAnimation = Core.ScenePool.Spawn(m_BreakingEffectPrefab, default, default)
                .GetComponent<BreakBlockAnimation>();

            breakAnimation.VTransform.position = VTransform.position;
            breakAnimation.BreakByBomb(explodingPoint, typeData.icon);

            ClearData();
            ResetRender();
        }

        public void HandleLose(Sprite icon, Sprite iconWithStar)
        {
            m_GrayRenderer.sprite = containStar ? iconWithStar : icon;
            m_GrayRenderer.color = TransparentColor;
            Tween.Alpha(m_GrayRenderer, Random.Range(0.6f, 0.8f), Random.Range(0.2f, 0.4f));
        }

        public void AnimateClean()
        {
            var cleanSeq = Sequence.Create();
            cleanSeq.Chain(Tween.Scale(VTransform, 0, 0.3f));
            cleanSeq.ChainCallback(() =>
            {
                ClearData();
                ResetRender();
                ResetBreakingOrder();
            });
            cleanSeq.Chain(Tween.Scale(VTransform, 1, 0.3f));
        }

        public void Clear()
        {
            m_ForegroundRenderer.sprite = null;
            m_GrayRenderer.sprite = null;
            typeData = null;
            state = GridElementState.NORMAL;
            brokenState = BrokenElementState.NONE;
        }

        public void ResetRender()
        {
            m_ForegroundRenderer.sprite = null;
            m_ForegroundRenderer.color = WhiteColor;
            m_ForegroundTransform.localScale = Vector3.one;
            m_GrayRenderer.sprite = null;
            m_GrayRenderer.color = WhiteColor;
        }

        public void ClearData()
        {
            typeData = null;
            containStar = false;
            state = GridElementState.NORMAL;
            brokenState = BrokenElementState.NONE;
        }

        public void ResetBreakingOrder()
        {
            breakingOrder = -1;
            breakingOrderWoody = -1;
        }

        #endregion
    }
}