using UnityEngine;

namespace OP.BlockPuzzle
{
    [CreateAssetMenu(menuName = "Tidi/GridElementBreakingFXWoodyStyleSettings")]
    public class GridElementBreakingFXWoodyStyleSettings : ScriptableObject
    {
        public Sprite[] oneLineFXs;
        public Sprite[] twoLineFXs;
        public Sprite[] threeLinesFXs;

        public Sprite GetSprite(int index, int brokenLines)
        {
            if (brokenLines <= 1)
                return oneLineFXs[Mathf.Clamp(index, 0, oneLineFXs.Length - 1)];
            // else if (brokenLines <= 2)
            //     return twoLineFXs[Mathf.Clamp(index, 0, twoLineFXs.Length - 1)];
            else
                return threeLinesFXs[Mathf.Clamp(index, 0, threeLinesFXs.Length - 1)];
        }
    }
}