using System.Collections;
using UnityEngine;
using PrimeTween;

namespace OP.BlockPuzzle
{
    public class GridElementBreakingFXWoodyStyle : MonoBehaviour
    {
        public static readonly Color StartAnimationColor = new Color(1, 1, 1, 0.6f);

        [SerializeField] SpriteRenderer _renderer;
        [SerializeField] GridElementBreakingFXWoodyStyleSettings _settings;

        public SpriteRenderer pRenderer => _renderer;

        Transform _transform;
        public Transform pTransform
        {
            get 
            {
                if (_transform == null)
                    _transform = transform;

                return _transform;
            }
        }

        public void Init(int order, int brokenLines)
        {
            _renderer.sprite = _settings.GetSprite(order, brokenLines);
        }

        public void Break(float duration)
        {
            StartCoroutine(YieldBreak(duration));
        }

        private IEnumerator YieldBreak(float duration)
        {
            float fadeInDuration = duration / 34f * 14f;
            float fadeOutDuration = duration / 34f * 20f;
            float delayScaleOutDuration = duration / 34f * 5f;
            float scaleOutDuration = duration / 34f * 29f;
            
            Sequence breakSequence = Sequence.Create();
            breakSequence.Insert(0f, Tween.Alpha(pRenderer,1f, fadeInDuration,Ease.Linear));
            breakSequence.Insert(fadeInDuration, Tween.Alpha(pRenderer,0.0f, fadeOutDuration, Ease.Linear));
            breakSequence.Insert(delayScaleOutDuration, Tween.Scale(pTransform,0f, scaleOutDuration, Ease.Linear));
            
            yield return breakSequence;

            Core.ScenePool.Recycle(pTransform.gameObject);
        }
    }
}