using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using OnePuz.Audio;
using OnePuz.Data;
using OnePuz.Services;
using UnityEngine;
using UnityEngine.Serialization;
using Random = UnityEngine.Random;

namespace OP.BlockPuzzle
{
    [System.Serializable]
    public class BlockSpawnData
    {
        public int id;
        public BlockAngle blockAngle;
        public BlockAngle lastSpawnAngle;
        public List<BlockAngle> availableAngles;
        public List<ChildrenCoordinateByAngle> childrenCoordinatesByAngleList;
        public bool differentAngleEachTurn;
        public int availableFromSpawnCount;
        public int limitSpawnedCountOnARow;
        public int spawnedCountOnARow;
        public bool canUseForFit;
        public bool uniqueInOneSpawn;
        public float probability;
        public GameObject prefab;
        public GridElementType type;

        public BlockAngle GetRandomAngle()
        {
            if (differentAngleEachTurn)
            {
                var randomAngle = lastSpawnAngle;
                var count = 0;
                while (randomAngle == lastSpawnAngle && count <= 10)
                {
                    var randomAngleIndex = Random.Range(0, availableAngles.Count);
                    randomAngle = availableAngles[randomAngleIndex];
                    count++;
                }

                return randomAngle;
            }

            {
                var randomAngleIndex = Random.Range(0, availableAngles.Count);
                return availableAngles[randomAngleIndex];
            }
        }

        public List<GridCoordinate> GetChildrenCoordinatesByAngle(BlockAngle angle)
        {
            for (int i = 0; i < childrenCoordinatesByAngleList.Count; i++)
            {
                if (childrenCoordinatesByAngleList[i].angle == angle)
                    return childrenCoordinatesByAngleList[i].childrenCoordinate;
            }

            return childrenCoordinatesByAngleList[0].childrenCoordinate;
        }
    }

    public enum PreloadGameType
    {
        Classic,
        Video1,
        Video2,
        Video3
    }

    public struct PreloadBlockData
    {
        public string BlockId;
        public BlockAngle BlockAngle;
        public GridElementType BlockElementType;

        public PreloadBlockData(string id, BlockAngle angle, GridElementType elementType)
        {
            BlockId = id;
            BlockAngle = angle;
            BlockElementType = elementType;
        }
    }

    public class BlockSpawner : BaseBehaviour
    {
        #region Fields

        [SerializeField] 
        private Transform _blockContainer;

        [SerializeField] 
        private Transform[] _spawningPositions;

        [Header("Boosters")] 
        [SerializeField]
        private GameObject _rotatePrefab;

        [SerializeField]
        private GameObject _bombPrefab;

        [Header("Configures")] 
        [SerializeField]
        private BlockConfigures _blockConfigures;

        #endregion

        #region Properties

        public Transform[] SpawningPositions => _spawningPositions;

        [FormerlySerializedAs("mBlockSpawnDataList")]
        [FormerlySerializedAs("m_BlockSpawnDataList")]
        [SerializeField]
        private List<BlockSpawnData> _blockSpawnDataList;

        private Block[] _blocks;

        [FormerlySerializedAs("mSpawnedCount")]
        [FormerlySerializedAs("m_SpawnedCount")]
        [SerializeField] private int _spawnedCount;

        private int SpawnedCount
        {
            get => _spawnedCount;
            set { _spawnedCount = value; }
        }

        private SpawnByTurnRule _currentSpawnRule;

        private List<Transform> _rotateAnimations;

        #endregion

        #region Unity Events

        private void OnValidate()
        {
            if (_spawningPositions.Length < 3)
                OLogger.LogError("Block Spawner: need 3 spawning positions");
        }

        #endregion

        #region Methods

        public void Configure(Action onComplete)
        {
            StartCoroutine(DoConfigure(onComplete));
        }

        private IEnumerator DoConfigure(Action onComplete)
        {
            _blocks = new Block[3];
            SpawnedCount = 0;
            ConfigureSpawnDataList();

            _rotateAnimations = new List<Transform>();
            yield return null;

            onComplete?.Invoke();
        }

        private void UpdateCurrentSpawnRule()
        {
            var currentSpawnRules = Core.Definition.BlockPuzzle.activeRule.rules;
            var spawnRuleCount = currentSpawnRules.Count;
            if (spawnRuleCount == 0)
                return;

            if (currentSpawnRules[spawnRuleCount - 1].endTurn < 0 &&
                SpawnedCount >= currentSpawnRules[spawnRuleCount - 1].startTurn)
                SpawnedCount = currentSpawnRules[spawnRuleCount - 1].repeatOnTurn;

            for (var i = 0; i < currentSpawnRules.Count; i++)
            {
                if (currentSpawnRules[i].startTurn > SpawnedCount ||
                    SpawnedCount > currentSpawnRules[i].endTurn) continue;
                _currentSpawnRule = currentSpawnRules[i];

                for (var j = 0; j < _blockSpawnDataList.Count; j++)
                {
                    _blockSpawnDataList[j].limitSpawnedCountOnARow =
                        _currentSpawnRule.GetSpawnLimitById(_blockSpawnDataList[j].id);
                    if (SpawnedCount == currentSpawnRules[i].startTurn)
                        _blockSpawnDataList[j].spawnedCountOnARow = 0;
                }

                break;
            }
        }

        private void ConfigureSpawnDataList()
        {
            _blockSpawnDataList = new List<BlockSpawnData>();

            for (var i = 0; i < _blockConfigures.blocks.Count; i++)
            {
                var data = _blockConfigures.blocks[i];
                var rule = new BlockSpawnData
                {
                    id = data.id,
                    blockAngle = BlockAngle.Angle0,
                    spawnedCountOnARow = 0,
                    limitSpawnedCountOnARow = -1,
                    availableAngles = data.availableAngles,
                    differentAngleEachTurn = data.differentAngleEachTurn,
                    childrenCoordinatesByAngleList = data.childrenCoordinateByAngleList,
                    canUseForFit = Core.Definition.BlockPuzzle.canFitBlockIdArray.Contains(data.id),
                    uniqueInOneSpawn = Core.Definition.BlockPuzzle.uniqueBlockIdInOneSpawnArray.Contains(data.id),
                    prefab = data.prefab
                };

                _blockSpawnDataList.Add(rule);
            }
        }

        private BlockSpawnData GetSpawnDataById(int id)
        {
            return _blockSpawnDataList.FirstOrDefault(t => t.id == id);
        }

        public void HandleLose()
        {
            DataShortcut.BlockPuzzle.BlockData = null;
        }

        public void Reset()
        {
            SpawnedCount = 0;

            for (var i = 0; i < _blocks.Length; i++)
            {
                if (!_blocks[i]) continue;
                _blocks[i].Reset();
                Core.ScenePool.Recycle(_blocks[i].VTransform.gameObject);
                _blocks[i] = null;
            }

            m_EnabledRotate = false;
            for (var i = 0; i < _rotateAnimations.Count; i++)
            {
                Core.ScenePool.Recycle(_rotateAnimations[i].gameObject);
                _rotateAnimations[i] = null;
            }

            _rotateAnimations.Clear();

            DataShortcut.BlockPuzzle.BlockData = null;
        }

        public void ShowBlocks()
        {
            for (var i = 0; i < _blocks.Length; i++)
            {
                if (_blocks[i] == null) continue;
                _blocks[i].ScaleWithChildren(0.5f, Board.BlockSize * 1, 0.2f);
            }

            if (m_EnabledRotate)
                EnableRotate();
        }

        public Block GetBlock(int index)
        {
            return _blocks[index];
        }

        public void RemoveBlock(int index)
        {
            if (_blocks[index])
            {
                _blocks[index] = null;
            }
        }

        // Check if all blocks has despawned, spawn new blocks
        public bool AnyBlockLeft()
        {
            var availableBlock = false;
            for (var i = 0; i < _blocks.Length; i++)
            {
                if (!_blocks[i]) continue;
                availableBlock = true;
                break;
            }

            return availableBlock;
        }

        public void DespawnBlock(int index)
        {
            if (_blocks[index] != null)
            {
                _blocks[index].Reset();
                Core.ScenePool.Recycle(_blocks[index].VTransform.gameObject);
                _blocks[index] = null;
            }

            // Check if all blocks has despawned, spawn new blocks
            var shouldBeSpawnNew = true;
            for (int i = 0; i < _blocks.Length; i++)
            {
                if (_blocks[i] == null) continue;
                shouldBeSpawnNew = false;
                break;
            }

            if (shouldBeSpawnNew)
            {
                SpawnAllBlocks();
            }
        }

        public void SpawnAllBlocks()
        {
            // SpawnAndShow3SingleBlocks();
            SpawnedCount++;
            UpdateCurrentSpawnRule();
            var shouldBeSpawnBlockFit = _currentSpawnRule.spawnCountForFit > 0 &&
                                        (_spawnedCount - _currentSpawnRule.startTurn) %
                                        _currentSpawnRule.spawnCountForFit == 0;
            var indices = new List<int> { 0, 1, 2 };
            indices.Shuffle();
            var containsUniqueBlock = false;

            for (var i = 0; i < indices.Count; i++)
            {
                var blockIndex = indices[i];
                var blockSpawnData = GetSpawnDataById(1);
                var blockType = GridElementType.Blue;
                var blockAngle = BlockAngle.Angle0;

                var validBlock = false;
                var triedCount = 0;
                while (!validBlock && triedCount < 3)
                {
                    blockSpawnData = GetRandomBlock(blockIndex, out blockType, out blockAngle,
                        i == 0 && shouldBeSpawnBlockFit);
                    if (!containsUniqueBlock || !blockSpawnData.uniqueInOneSpawn)
                    {
                        validBlock = true;
                    }

                    triedCount++;
                }

                if (!validBlock)
                {
                    blockSpawnData = GetSpawnDataById(1);
                    OLogger.LogWarning("Can not find unique block for index:", blockIndex);
                }

                if (blockSpawnData.uniqueInOneSpawn)
                    containsUniqueBlock = true;

                var coordinates =
                    new List<GridCoordinate>(blockSpawnData.GetChildrenCoordinatesByAngle(blockAngle));
                coordinates.Shuffle();
                var starCoordinates = new List<GridCoordinate>();

                var block = SpawnBlock(
                    blockSpawnData.prefab.transform,
                    blockSpawnData.id,
                    blockType,
                    blockAngle,
                    starCoordinates);

                blockSpawnData.spawnedCountOnARow++;
                blockSpawnData.lastSpawnAngle = blockAngle;

                _blocks[blockIndex] = block;
                _blocks[blockIndex].VTransform.localScale = Vector3.zero;
                _blocks[blockIndex].ConfigureOriginalPosition(_spawningPositions[blockIndex].localPosition);
            }
        }

        private Block SpawnBlock(Transform prefabTrans, int id, GridElementType type, BlockAngle angle,
            List<GridCoordinate> starCoordinates)
        {
            var block = Core.ScenePool.Spawn(prefabTrans.gameObject, _blockContainer, default, default)
                .GetComponent<Block>();

            block.Configure(id, angle, starCoordinates);
            block.ConfigureType(Core.Manager_BlockPuzzle.GridElementTypeConfigures.GetDataByType(type));

            // Update block type collections
            Core.Manager_BlockPuzzle.Board.TypeCountDictionary[type] += block.RelativeGridCoordinates.Count;

            return block;
        }

        private BlockSpawnData GetRandomBlock(int blockIndex, out GridElementType blockType, out BlockAngle blockAngle,
            bool shouldSpawnFit = false)
        {
            BlockSpawnData blockSpawnData = null;
            blockType = GetRandomBlockType();
            blockAngle = BlockAngle.Angle0;

            var fitBlockId = -1;
            var availableFitBlock = false;
            int coordX = -1, coordY = -1;
            int breakingLines = 0;

            if (shouldSpawnFit)
            {
                availableFitBlock = FindBlockForBestMatching(out fitBlockId, out blockAngle, out coordX, out coordY,
                    out breakingLines);
            }

            if (availableFitBlock)
            {
                blockSpawnData = GetSpawnDataById(fitBlockId);
            }
            else
            {
                string groupIdString;
                List<int> canSpawnIds;
                if (blockIndex == 0)
                {
                    groupIdString = _currentSpawnRule.leftGroupIds;
                    canSpawnIds = _currentSpawnRule.leftBlockIds;
                }
                else if (blockIndex == 1)
                {
                    groupIdString = _currentSpawnRule.midGroupIds;
                    canSpawnIds = _currentSpawnRule.midBlockIds;
                }
                else
                {
                    groupIdString = _currentSpawnRule.rightGroupIds;
                    canSpawnIds = _currentSpawnRule.rightBlockIds;
                }

                int count = 0;
                while (blockSpawnData == null ||
                       (blockSpawnData.spawnedCountOnARow >= blockSpawnData.limitSpawnedCountOnARow &&
                        blockSpawnData.limitSpawnedCountOnARow != -1))
                {
                    int randomId = (canSpawnIds.Count == 0)
                        ? Random.Range(1, 10)
                        : canSpawnIds[Random.Range(0, canSpawnIds.Count)];
                    blockSpawnData = GetSpawnDataById(randomId);
                    count++;
                    if (count > 100)
                        break;
                }

                // Random angle
                blockAngle = blockSpawnData.GetRandomAngle();
            }

            return blockSpawnData;
        }

        public GridElementType GetRandomBlockType()
        {
            Dictionary<GridElementType, int> typeCountCollection = Core.Manager_BlockPuzzle.Board.TypeCountDictionary;
            int blockTypeCount = Core.Manager_BlockPuzzle.BlockTypeCount;
            float[] probabilities = new float[blockTypeCount];
            float avaragePercentageFactor = 100f / blockTypeCount;
            float totalCount = 0;
            for (int i = 1; i < blockTypeCount + 1; i++)
            {
                totalCount += typeCountCollection[(GridElementType)i];
            }

            float totalProbability = 0f;
            for (int i = 1; i < blockTypeCount + 1; i++)
            {
                float percentage = typeCountCollection[(GridElementType)i] * 100f / totalCount;
                if (percentage >= avaragePercentageFactor)
                    probabilities[i - 1] = 0f;
                else
                    probabilities[i - 1] = avaragePercentageFactor - percentage;

                totalProbability += probabilities[i - 1];
            }

            float randomProbabilityPoint = Random.value * totalProbability;

            for (int i = 0; i < probabilities.Length; i++)
            {
                float probability = probabilities[i];
                if (randomProbabilityPoint <= probability)
                {
                    return (GridElementType)(i + 1);
                }

                randomProbabilityPoint -= probability;
            }

            OLogger.LogNotice("Spawner: Can't find a balanced block type ");
            return (GridElementType)Random.Range(1, blockTypeCount + 1);
        }

        private List<GridCoordinate> m_FindBestFit_CheckingBlockCoordinates = new(10);
        private GridElement[] m_FindBestFit_CheckingGridElementArray = new GridElement[10];
        private int m_FindBestFit_CheckingGridElementCount;

        public bool FindBlockForBestMatching(out int bestFitBlockId, out BlockAngle bestFitBlockAngle,
            out int outCoordX, out int outCoordY, out int breakingLines)
        {
            Board board = Core.Manager_BlockPuzzle.Board;
            int gridSize = Board.GridNumber;
            breakingLines = 0;
            bool availableFitBlock = false;
            int bestFitBlockElementCount = 0;
            bestFitBlockId = -1;
            bestFitBlockAngle = BlockAngle.Angle0;
            outCoordX = -1;
            outCoordY = -1;
            int blockSpawnDataCount = _blockSpawnDataList.Count;
            for (int x = 0; x < gridSize; x++)
            {
                for (int y = 0; y < gridSize; y++)
                {
                    GridElement element = board.GetElement(x, y);
                    if (element.typeData != null)
                        continue;

                    for (int i = 0; i < blockSpawnDataCount; i++)
                    {
                        BlockSpawnData data = _blockSpawnDataList[i];
                        if (!data.canUseForFit)
                            continue;

                        for (int angleIndex = 0; angleIndex < data.availableAngles.Count; angleIndex++)
                        {
                            BlockAngle blockAngle = data.availableAngles[angleIndex];

                            // m_FindBestFit_CheckingGridElements.Clear();
                            m_FindBestFit_CheckingGridElementCount = 0;
                            m_FindBestFit_CheckingBlockCoordinates = data.GetChildrenCoordinatesByAngle(blockAngle);

                            bool validGridCoord = true;
                            for (int coordIndex = 0;
                                 coordIndex < m_FindBestFit_CheckingBlockCoordinates.Count;
                                 coordIndex++)
                            {
                                int coordX = x + m_FindBestFit_CheckingBlockCoordinates[coordIndex].x;
                                int coordY = y + m_FindBestFit_CheckingBlockCoordinates[coordIndex].y;
                                if (coordX < 0 || coordX >= gridSize || coordY < 0 || coordY >= gridSize)
                                {
                                    validGridCoord = false;
                                    break;
                                }

                                if (board.GetElement(coordX, coordY).typeData != null)
                                {
                                    validGridCoord = false;
                                    break;
                                }

                                // m_FindBestFit_CheckingGridElements.Add(board.GetElement(coordX, coordY));
                                m_FindBestFit_CheckingGridElementArray[coordIndex] = board.GetElement(coordX, coordY);
                                m_FindBestFit_CheckingGridElementCount++;
                            }

                            // if (validGridCoord && m_FindBestFit_CheckingBlockCoordinates.Count == m_FindBestFit_CheckingGridElements.Count)
                            if (validGridCoord && m_FindBestFit_CheckingBlockCoordinates.Count ==
                                m_FindBestFit_CheckingGridElementCount)
                            {
                                int localBreakingLines = 0;
                                // int childrenCoordinateCount = data.GetChildrenCoordinatesByAngle(blockAngle).Count;
                                board.CheckElementsCanBreak(m_FindBestFit_CheckingGridElementArray,
                                    m_FindBestFit_CheckingGridElementCount, out localBreakingLines);
                                if (localBreakingLines > breakingLines || (localBreakingLines == breakingLines &&
                                                                           localBreakingLines >= 1 &&
                                                                           bestFitBlockElementCount >
                                                                           m_FindBestFit_CheckingGridElementCount))
                                {
                                    breakingLines = localBreakingLines;
                                    bestFitBlockId = data.id;
                                    bestFitBlockAngle = blockAngle;
                                    bestFitBlockElementCount = m_FindBestFit_CheckingGridElementCount;

                                    outCoordX = x;
                                    outCoordY = y;

                                    availableFitBlock = true;
                                }
                            }
                        }
                    }
                }
            }

            for (int i = 0; i < m_FindBestFit_CheckingGridElementArray.Length; i++)
                m_FindBestFit_CheckingGridElementArray[i] = null;

            return availableFitBlock;
        }

        private int[] _starProbabilities = { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1 };

        private int GetRandomTotalStarsForOneSpawningBlock()
        {
            int openedGameTime = DataShortcut.User.openedGameTime;
            int chance = 30;
            if (openedGameTime <= 2)
            {
                if (SpawnedCount < 10)
                    chance = 10;
                else if (SpawnedCount < 50)
                    chance = 20;
            }
            else
            {
                if (SpawnedCount < 10)
                    chance = 20;
                else if (SpawnedCount < 50)
                    chance = 30;
            }

            return Random.Range(0, chance) == 1 ? 1 : 0;
        }

        public void SpawnAndShow3SingleBlocks()
        {
            for (int i = 0; i < _blocks.Length; i++)
            {
                if (_blocks[i] != null)
                {
                    _blocks[i].Reset();
                    Core.ScenePool.Recycle(_blocks[i].VTransform.gameObject);
                    _blocks[i] = null;
                }

                List<GridCoordinate> starCoordinates = new List<GridCoordinate>();
                BlockSpawnData rule = _blockSpawnDataList[0];

                var block = Core.ScenePool.Spawn(rule.prefab, _blockContainer, default, default).GetComponent<Block>();

                block.VTransform.localPosition = Vector3.zero;
                block.VTransform.localScale = Vector3.zero;
                block.Configure(rule.id, BlockAngle.Angle0, starCoordinates);
                block.ConfigureType(Core.Manager_BlockPuzzle.GridElementTypeConfigures.GetRandomData());
                block.ConfigureOriginalPosition(_spawningPositions[i].localPosition);
                _blocks[i] = block;

                rule.spawnedCountOnARow++;
            }

            SpawnedCount++;

            ShowBlocks();
        }

        public Vector2 GetTutorialBlockWorldPosition()
        {
            return _spawningPositions[1].position;
        }

        public void HandleSelectedBlock()
        {
        }

        #region Booster Rotate

        private bool m_EnabledRotate;
        public List<BlockAngle> m_OriginalAnglesBeforeRotating = new List<BlockAngle>(3);

        public void EnableRotate()
        {
            m_EnabledRotate = true;

            for (int i = 0; i < _blocks.Length; i++)
            {
                _blocks[i]?.TurnRotateMode(true);

                if (_rotateAnimations.Count <= i)
                {
                    var rotateAnimation = Core.ScenePool.Spawn(_rotatePrefab.gameObject, Vector3.zero, default)
                        .transform;
                    _rotateAnimations.Add(rotateAnimation);
                }

                _rotateAnimations[i].position = _spawningPositions[i].position;
            }

            UpdateRotateStateOfAllBlocks();
        }

        public void DisableRotate()
        {
            m_EnabledRotate = false;
            for (int i = 0; i < _rotateAnimations.Count; i++)
            {
                if (_rotateAnimations[i] != null)
                    Core.ScenePool.Recycle(_rotateAnimations[i]);

                _rotateAnimations[i] = null;
            }

            _rotateAnimations.Clear();

            for (int i = 0; i < _blocks.Length; i++)
            {
                _blocks[i]?.TurnRotateMode(false);
            }
        }

        public bool CheckIfUserRotatedBlocks()
        {
            if (m_OriginalAnglesBeforeRotating.Count == 0)
                return false;

            for (int i = 0; i < _blocks.Length; i++)
            {
                BlockAngle angle = _blocks[i] != null ? _blocks[i].BlockAngle : BlockAngle.Angle0;
                if (m_OriginalAnglesBeforeRotating[i] != angle)
                    return true;
            }

            return false;
        }

        public bool CheckIfUserRotatedBlock(int index, BlockAngle currentAngle)
        {
            if (index < 0 || index >= m_OriginalAnglesBeforeRotating.Count)
                return false;

            return m_OriginalAnglesBeforeRotating[index] != currentAngle;
        }

        public void UpdateRotateStateOfAllBlocks()
        {
            m_OriginalAnglesBeforeRotating.Clear();

            for (int i = 0; i < _blocks.Length; i++)
            {
                m_OriginalAnglesBeforeRotating.Add(_blocks[i] != null ? _blocks[i].BlockAngle : BlockAngle.Angle0);
            }
        }

        #endregion

        #region Booster Switch

        public void SwitchBlocks()
        {
            StartCoroutine(YieldSwitchBlocks());
        }

        private IEnumerator YieldSwitchBlocks()
        {
            for (int i = 0; i < _blocks.Length; i++)
            {
                if (_blocks[i] != null)
                {
                    _blocks[i].Break();
                    _blocks[i].Reset();
                    Core.ScenePool.Recycle(_blocks[i].VTransform.gameObject);
                    _blocks[i] = null;
                }
            }

            yield return null;
            for (int i = 0; i < _blocks.Length; i++)
            {
                var rule = GetSpawnDataById(1);
                _blocks[i] = SpawnBlock(rule.prefab.transform, rule.id,
                    Core.Manager_BlockPuzzle.GridElementTypeConfigures.GetRandomData().type, BlockAngle.Angle0,
                    new List<GridCoordinate>());
                _blocks[i].ConfigureOriginalPosition(_spawningPositions[i].localPosition);
            }

            yield return null;
            ShowBlocks();

            if (m_EnabledRotate)
                EnableRotate();
        }

        #endregion

        #region Booster Bomb

        BlockBomb _blockBomb;

        public BlockBomb GetBlockBomb()
        {
            return _blockBomb;
        }

        public void UseBoosterBomb()
        {
            _blockBomb = Core.ScenePool.Spawn(_bombPrefab, _blockContainer, default, default)
                .GetComponent<BlockBomb>();

            _blockBomb.Configure();
            _blockBomb.VTransform.localScale = Vector3.zero;
            _blockBomb.ConfigureOriginalPosition(_spawningPositions[1].localPosition);
            _blockBomb.Scale(1f, 0.3f);

            for (int i = 0; i < _blocks.Length; i++)
                _blocks[i]?.ScaleWithChildren(0, Board.BlockSize * 1, 0.1f);
        }

        public void CancelBoosterBomb()
        {
            if (_blockBomb != null)
            {
                Core.ScenePool.Recycle(_blockBomb.VTransform.gameObject);
                _blockBomb = null;
            }

            for (int i = 0; i < _blocks.Length; i++)
                _blocks[i]?.ScaleWithChildren(0.5f, Board.BlockSize * 1, 0.2f);
        }

        #endregion

        #region Block Spawn Archives

        public void LoadSavedBlocks()
        {
            var data = DataShortcut.BlockPuzzle.BlockData;
            if (data == null || data.blocks.All(d => d.numberId == -1 || d.type == -1))
            {
                SpawnAllBlocks();
            }
            else
            {
                SpawnedCount = data.spawnedCount;
                LoadBlocksFromDto(data);

                if (_blockSpawnDataList.Count == data.allBlockSpawnedCount.Count)
                {
                    for (var i = 0; i < data.allBlockSpawnedCount.Count; i++)
                    {
                        _blockSpawnDataList[i].spawnedCountOnARow = data.allBlockSpawnedCount[i];
                    }
                }

                if (_blockSpawnDataList.Count == data.lastSpawnedAngles.Count)
                {
                    for (int i = 0; i < data.lastSpawnedAngles.Count; i++)
                    {
                        _blockSpawnDataList[i].lastSpawnAngle = (BlockAngle)data.lastSpawnedAngles[i];
                    }
                }

                UpdateCurrentSpawnRule();
            }
        }

        public void LoadBlocksFromDto(BlockSaveModelDto data)
        {
            OLogger.LogNotice("Load Blocks From DTO");
            int validBlockCount = 0;

            for (int i = 0; i < _blocks.Length; i++)
            {
                if (_blocks[i] != null)
                    Core.ScenePool.Recycle(_blocks[i].VTransform.gameObject);

                if (i >= data.blocks.Count)
                {
                    GridElementType blockType = GridElementType.Blue;
                    BlockAngle blockAngle = BlockAngle.Angle0;
                    BlockSpawnData blockSpawnData = GetRandomBlock(i, out blockType, out blockAngle);

                    Block block = SpawnBlock(
                        blockSpawnData.prefab.transform,
                        blockSpawnData.id,
                        blockType,
                        blockAngle,
                        new List<GridCoordinate>());

                    blockSpawnData.spawnedCountOnARow++;
                    blockSpawnData.lastSpawnAngle = blockAngle;

                    UpdateCurrentSpawnRule();

                    _blocks[i] = block;
                    _blocks[i].ConfigureOriginalPosition(_spawningPositions[i].localPosition);
                }
                else
                {
                    var blockDto = data.blocks[i];
                    if (blockDto.numberId == -1 && blockDto.type == -1)
                    {
                        _blocks[i] = null;
                        continue;
                    }

                    var blockId = blockDto.numberId;
                    var blockType = (GridElementType)blockDto.type;
                    var blockAngle = (BlockAngle)blockDto.blockAngle;

                    var starCoordinates = new List<GridCoordinate>();
                    if (blockDto.starCoords != null)
                    {
                        foreach (var coord in blockDto.starCoords)
                        {
                            starCoordinates.Add(new GridCoordinate(coord.coordX, coord.coordY));
                        }
                    }

                    if (blockId > 0)
                        validBlockCount++;

                    // Nếu là block cuối mà vẫn chưa có block hợp lệ nào, tạo 1 block
                    if (blockId > 0 || (i == _blocks.Length - 1 && validBlockCount == 0))
                    {
                        var block = Core.ScenePool.Spawn(_blockConfigures.GetBlockById(blockId).prefab,
                            _blockContainer, default, default).GetComponent<Block>();

                        block.VTransform.localPosition = Vector3.zero;
                        block.VTransform.localScale = Vector3.zero;
                        block.Configure(blockId, blockAngle, starCoordinates);
                        block.ConfigureType(Core.Manager_BlockPuzzle.GridElementTypeConfigures.GetDataByType(blockType));
                        block.ConfigureOriginalPosition(_spawningPositions[i].localPosition);

                        _blocks[i] = block;
                    }
                    else
                    {
                        _blocks[i] = null;
                    }
                }
            }
        }


        public void SaveBlocks()
        {
            var saveModel = new BlockSaveModelDto();

            for (var i = 0; i < _blocks.Length; i++)
            {
                if (_blocks[i] != null)
                {
                    saveModel.blocks.Add(_blocks[i].ToDto());
                }
                else
                {
                    saveModel.blocks.Add(new BlockDataDto
                    {
                        numberId = -1,
                        type = -1,
                        blockAngle = (int)BlockAngle.Angle0,
                        starCoords = new List<GridCoordinateDto>()
                    });
                }
            }

            saveModel.spawnedCount = SpawnedCount;

            for (var i = 0; i < _blockSpawnDataList.Count; i++)
            {
                saveModel.allBlockSpawnedCount.Add(_blockSpawnDataList[i].spawnedCountOnARow);
                saveModel.lastSpawnedAngles.Add((int)_blockSpawnDataList[i].lastSpawnAngle);
            }

            DataShortcut.BlockPuzzle.BlockData = saveModel;
        }

        #endregion

        #region Editor Functions

        public void ChangeBlock(int blockIndex, int blockId, BlockAngle angle, GridElementType blockType)
        {
            if (_blocks[blockIndex] != null)
                Core.ScenePool.Recycle(_blocks[blockIndex].VTransform.gameObject);

            var block = Core.ScenePool
                .Spawn(_blockConfigures.GetBlockById(blockId).prefab, _blockContainer, default, default)
                .GetComponent<Block>();

            block.VTransform.localPosition = Vector3.zero;
            block.VTransform.localScale = Vector3.zero;
            block.Configure(blockId, angle, new List<GridCoordinate>());
            block.ConfigureType(Core.Manager_BlockPuzzle.GridElementTypeConfigures.GetDataByType(blockType));
            block.ConfigureOriginalPosition(_spawningPositions[blockIndex].localPosition);

            block.ScaleWithChildren(0.5f, Board.BlockSize * 1, 0.2f);

            _blocks[blockIndex] = block;
        }

        public void ChangeAllBlocks(int blockId, BlockAngle angle, GridElementType blockType)
        {
            for (int i = 0; i < _blocks.Length; i++)
            {
                ChangeBlock(i, blockId, angle, blockType);
            }
        }

        #endregion

        #endregion
    }
}