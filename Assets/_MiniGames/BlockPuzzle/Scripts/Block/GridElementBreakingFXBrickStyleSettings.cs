using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace OP.BlockPuzzle
{
    [CreateAssetMenu(menuName = "Tidi/GridElementBreakingFXBrickStyleSettings")]
    public class GridElementBreakingFXBrickStyleSettings : ScriptableObject
    {
        public List<Data> fxs;
        
        public GameObject GetPrefab(GridElementType type)
        {
            var datum = fxs.FirstOrDefault(x => x.type == type);
            if (datum == null)
                return null;
            
            else return datum.prefab;
        }

        [System.Serializable]
        public class Data 
        {
            public GridElementType type;
            public GameObject prefab;
        }
    }
}