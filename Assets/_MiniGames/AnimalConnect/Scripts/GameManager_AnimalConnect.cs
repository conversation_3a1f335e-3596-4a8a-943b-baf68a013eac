using Cysharp.Threading.Tasks;
using OnePuz.Managers;

namespace OP.AnimalConnect
{
    public class GameManager_AnimalConnect : BaseManager
    {
        public override UniTask LoadAsync()
        {
            return UniTask.CompletedTask;
        }

        public override UniTask UnloadAsync()
        {
            return UniTask.CompletedTask;
        }

        public override UniTask ResetAsync()
        {
            return UniTask.CompletedTask;
        }

        public override void Activate()
        {
        }
    }
}