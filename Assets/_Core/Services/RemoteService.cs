using System;
using System.Collections.Generic;
using Firebase.Extensions;
using Firebase.RemoteConfig;
using Newtonsoft.Json;

namespace OnePuz.Services
{
    public class RemoteService : IService
    {
        private bool _hasFetched;
        private bool _fetchedSuccessful;

        public bool HasFetched => _hasFetched;

        public void FetchData()
        {
            OLogger.Log("Remote is fetching data...");

            _hasFetched = false;

            // FetchAsync only fetches new data if the current data is older than the provided
            // timespan. Otherwise, it assumes the data is "recent enough", and does nothing.
            // By default, the timespan is 12 hours, and for production apps, this is a good
            // number.  For this example though, it's set to a timespan of zero, so that
            // changes in the console will always show up immediately.
            var fetchTask = FirebaseRemoteConfig.DefaultInstance.FetchAsync(System.TimeSpan.Zero);
            fetchTask.ContinueWithOnMainThread(FetchComplete);
        }

        void FetchComplete(System.Threading.Tasks.Task fetchTask)
        {
            if (fetchTask.IsCanceled)
            {
                OLogger.Log("Fetch canceled.");
            }
            else if (fetchTask.IsFaulted)
            {
                OLogger.Log("<PERSON><PERSON> encountered an error.");
            }
            else if (fetchTask.IsCompleted)
            {
                OLogger.Log("Fetch completed successfully!");
            }

            var info = FirebaseRemoteConfig.DefaultInstance.Info;

            switch (info.LastFetchStatus)
            {
                case LastFetchStatus.Success:
                    FirebaseRemoteConfig.DefaultInstance.ActivateAsync();

                    OLogger.Log($"Remote data loaded and ready (last fetch time {info.FetchTime}).");
                    break;
                case LastFetchStatus.Failure:
                    switch (info.LastFetchFailureReason)
                    {
                        case FetchFailureReason.Error:
                            OLogger.Log("Fetch failed for unknown reason");
                            break;
                        case FetchFailureReason.Throttled:
                            OLogger.Log("Fetch throttled until " + info.ThrottledEndTime);
                            break;
                    }

                    break;
                case LastFetchStatus.Pending:
                    OLogger.Log("Latest Fetch call still pending.");
                    break;
            }

            _hasFetched = info.LastFetchStatus == LastFetchStatus.Success;
        }

        public bool GetValue<T>(string key, out T output)
        {
            output = default;
            if (!_hasFetched)
                return false;
            
            var remote = FirebaseRemoteConfig.DefaultInstance;
            
            var type = typeof(T);
        
            if (type == typeof(string))
            {
                output = (T)(object)remote.GetValue(key).StringValue;
                return true;
            }
            else if (type == typeof(double))
            {
                output = (T)(object)remote.GetValue(key).DoubleValue;
                return true;
            }
            else if (type == typeof(long))
            {
                output = (T)(object)remote.GetValue(key).LongValue;
                return true;
            }
            else if (type == typeof(int))
            {
                output = (T)Convert.ChangeType(remote.GetValue(key).LongValue, type);
                return true;
            }
            else if (type == typeof(bool))
            {
                output = (T)(object)remote.GetValue(key).BooleanValue;
                return true;
            }
            else if (type == typeof(float))
            {
                output = (T)(object)remote.GetValue(key).DoubleValue;
                return true;
            }
            else if (type.IsEnum)
            {
                var enumType = typeof(T);
                var intValue = (int)remote.GetValue(key).LongValue;
            
                output = (T)Enum.ToObject(enumType, intValue);
                return true;
            }
            else if ((type.IsClass || type.IsArray || type.IsConstructedGenericType))
            {
                var jsonValue = remote.GetValue(key).StringValue;
        
                if (string.IsNullOrEmpty(jsonValue))
                    return false;
            
                try
                {
                    output = JsonConvert.DeserializeObject<T>(jsonValue);
                    return true;
                }
                catch (Exception ex)
                {
                    OLogger.LogError($"Error deserializing value for key {key}: {ex.Message}");
                    return false;
                }
            }
            else
            {
                return false;
            }
        }
    }
}