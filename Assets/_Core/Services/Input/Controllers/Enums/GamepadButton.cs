// SPDX-License-Identifier: MIT
// Copyright (c) 2016-2021 <PERSON> (@JuDelCo)

namespace OnePuz.InputHandler
{
	public enum GamepadButton
	{
		None = 0,
		A = 1,
		B = 2,
		X = 3,
		Y = 4,
		L = 5,
		R = 6,
		Select = 7,
		Start = 8,
		Home = 9,
		<PERSON><PERSON><PERSON> = 10,
		<PERSON>_<PERSON> = 11,
		XBox_View = Select,
		XBox_Menu = Start,
		XBox_Button = Home,
		PS_Share = Select,
		PS_Options = Start,
		PS_Button = Home,
		PS_Touch = 14,
		Button_1 = A,
		Button_2 = B,
		Button_3 = X,
		Button_4 = Y,
		Button_5 = L,
		Button_6 = R,
		Button_7 = Select,
		Button_8 = Start,
		Button_9 = Home,
		Button_10 = <PERSON>_Stick,
		But<PERSON>_11 = <PERSON>_<PERSON>,
		But<PERSON>_12 = 12,
		Button_13 = 13,
		Button_14 = PS_Touch,
		Button_15 = 15,
		But<PERSON>_16 = 16,
		But<PERSON>_17 = 17,
		But<PERSON>_18 = 18,
		But<PERSON>_19 = 19,
		But<PERSON>_20 = 20
	}
}
