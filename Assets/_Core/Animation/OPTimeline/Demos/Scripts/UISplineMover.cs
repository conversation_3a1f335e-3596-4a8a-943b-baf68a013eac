using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Splines;

public class UISplineMover : MonoBehaviour
{
    public SplineContainer splineContainer; // Reference to the SplineContainer
    public RectTransform uiElement; // Reference to the UI element
    public float duration = 5f; // Duration of the movement along the spline

    private float elapsedTime = 0f;
    private Canvas _canvas;
    private Camera _camera;

    void Start()
    {
        if (uiElement != null)
        {
            _canvas = uiElement.GetComponentInParent<Canvas>();
            if (_canvas == null)
            {
                Debug.LogError("Canvas component not found in parent hierarchy of the UI element.");
            }
            else 
            {
                _camera = _canvas.worldCamera;
                
                if (_camera == null)
                    _camera = Camera.main;
            }
        }
    }

    void Update()
    {
        if (splineContainer == null || uiElement == null || _canvas == null)
            return;

        elapsedTime += Time.deltaTime;
        float t = Mathf.PingPong(elapsedTime / duration, 1f); // Loop the movement

        // Sample the position along the spline
        Vector3 splinePosition = splineContainer.EvaluatePosition(t);

        // Convert the spline position to Canvas coordinates
        Vector2 canvasPosition = WorldToCanvasPosition(splinePosition);

        // Move the UI element
        uiElement.anchoredPosition = canvasPosition;
    }

    Vector2 WorldToCanvasPosition(Vector3 worldPosition)
    {
        // Convert world position to screen point
        Vector2 screenPoint = RectTransformUtility.WorldToScreenPoint(_camera, worldPosition);

        // Convert screen point to local position in the canvas
        RectTransformUtility.ScreenPointToLocalPointInRectangle(_canvas.GetComponent<RectTransform>(), screenPoint, _camera, out Vector2 localPoint);

        return localPoint;
    }
}