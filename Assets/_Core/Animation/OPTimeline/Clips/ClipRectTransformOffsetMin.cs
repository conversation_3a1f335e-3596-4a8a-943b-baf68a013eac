using UnityEngine;

namespace OnePuz.OPTimeline
{
    [System.Serializable]
    [OPClipCreate("RectTransform/Offset Min", "Offset Min")]
    public class ClipRectTransformOffsetMin : OPTweenClip<Vector2,RectTransform>
    {
        protected override Vector2 GetCurrentValue()
        {
            return target.offsetMin;
        }

        protected override void SetValue(Vector2 newValue)
        {
            target.offsetMin = newValue;
        }
        
        protected override void OnUpdate(OPEvaluateState state, float timelineTime, float clipTime,
            float normalizedClipTime, bool previewMode)
        {
            if(state != OPEvaluateState.Running) return;
            SetValue(Vector2.LerpUnclamped(CurrentFrom, to, ease.Lerp(normalizedClipTime)));
        }
    }
}