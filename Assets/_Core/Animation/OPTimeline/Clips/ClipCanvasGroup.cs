using UnityEngine;

namespace OnePuz.OPTimeline
{
    [System.Serializable]
    [OPClipCreate("Canvas/Canvas Group Alpha", "Canvas Group Alpha")]
    public class ClipCanvasGroup : OPTweenClip<float,CanvasGroup>
    {
        protected override float GetCurrentValue()
        {
            return target.alpha;
        }

        protected override void SetValue(float newValue)
        {
            target.alpha = newValue;
        }
        
        protected override void OnUpdate(OPEvaluateState state, float timelineTime, float clipTime,
            float normalizedClipTime, bool previewMode)
        {
            if(state != OPEvaluateState.Running) return;
            SetValue(Mathf.LerpUnclamped(CurrentFrom, to, ease.Lerp(normalizedClipTime)));
        }
    }
}