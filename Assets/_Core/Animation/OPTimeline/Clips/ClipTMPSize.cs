using TMPro;
using UnityEngine;

namespace OnePuz.OPTimeline
{
    [System.Serializable]
    [OPClipCreate("Text Mesh Pro/Size", "Animate Text Size")]
    public class ClipTMPSize : OPTweenClip<float,TMP_Text>
    {
        protected override float GetCurrentValue()
        {
            return target.fontSize;
        }

        protected override void SetValue(float newValue)
        {
            target.fontSize = newValue;
        }
        
        protected override void OnUpdate(OPEvaluateState state, float timelineTime, float clipTime,
            float normalizedClipTime, bool previewMode)
        {
            if(state != OPEvaluateState.Running) return;
            SetValue(Mathf.LerpUnclamped(CurrentFrom, to, ease.Lerp(normalizedClipTime)));
        }
    }
}