using System.Threading;
using Cysharp.Threading.Tasks;
#if PRIME_TWEEN_INSTALLED
using JetBrains.Annotations;
using PrimeTween;
#endif
using UnityEngine;

namespace OnePuz.Extensions
{
    public static class PrimeTweenExtensions
    {
#if PRIME_TWEEN_INSTALLED
        public static UniTask ToUniTask(this PrimeTween.Tween tween, CancellationToken cancellationToken)
        {
            return tween.ToYieldInstruction().ToUniTask(cancellationToken: cancellationToken);
        }

        public static UniTask ToUniTask(this PrimeTween.Sequence sequence, CancellationToken cancellationToken)
        {
            return sequence.ToYieldInstruction().ToUniTask(cancellationToken: cancellationToken);
        }
        
        public static void Forget(this PrimeTween.Tween tween, CancellationToken cancellationToken)
        {
            tween.ToYieldInstruction().ToUniTask(cancellationToken: cancellationToken).Forget();
        }
        
        public static void Forget(this PrimeTween.Tween tween)
        {
            tween.ToYieldInstruction().ToUniTask().Forget();
        }

        public static void Forget(this PrimeTween.Sequence sequence, CancellationToken cancellationToken)
        {
            sequence.ToYieldInstruction().ToUniTask(cancellationToken: cancellationToken).Forget();
        }
#endif
    }
}