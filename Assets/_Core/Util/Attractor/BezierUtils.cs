using PrimeTween;
using UnityEngine;
using Unity.Mathematics;

/// <summary>
/// Utility class for Bezier curve calculations
/// </summary>
public static class BezierUtils
{
    #region Calculation

    /// <summary>
    /// Calculate control points for a cubic Bezier curved with path curvature
    /// </summary>
    public static void CalculateControlPoints(Vector3 a, Vector3 b, float curvature, float dropFactor, bool use3D, Vector3 offsetDirection, out Vector3 c, out Vector3 d)
    {
        var direction = (b - a).normalized;
        var distance = Vector3.Distance(a, b);

        Vector3 perp;

        if (use3D)
        {
            // In 3D mode, use the specified offset direction
            perp = Vector3.Cross(direction, offsetDirection).normalized;

            // If the result is too small (direction and offsetDirection are nearly parallel)
            if (perp.magnitude < 0.01f)
            {
                // Try a different direction
                perp = Vector3.Cross(direction, direction.z > 0.5f ? Vector3.up : Vector3.forward).normalized;
            }
        }
        else
        {
            // In 2D mode (XY plane), always use forward vector for consistency
            perp = Vector3.Cross(direction, Vector3.forward).normalized;

            // If the direction is too vertical, use a different reference vector
            if (perp.magnitude < 0.01f)
            {
                perp = Vector3.Cross(direction, Vector3.right).normalized;
            }
        }

        // Determine if we need to invert the curve direction based on relative positions
        bool invertCurve = a.x > b.x; // If object is to the right of target, invert curve
        if (invertCurve)
        {
            perp = -perp; // Invert the perpendicular direction
        }

        // Position control points at 1/3 and 2/3 along the path
        float fractionC = 0.33f;
        float fractionD = 0.66f;

        // Calculate offset magnitude based on distance and curvature
        float offsetMagnitude = distance * 0.5f * curvature;

        // Position control points with offset
        c = a + (b - a) * fractionC + perp * offsetMagnitude;
        d = a + (b - a) * fractionD + perp * offsetMagnitude;

        // Apply vertical offset to control point C
        // This creates a curved path that dips down or rises up depending on the relative positions
        bool targetIsHigher = b.y > a.y;
        
        if (targetIsHigher)
        {
            // Target is higher - create a path that rises up with a slight dip first
            c.y -= distance * dropFactor;
        }
        else
        {
            // Target is lower - create a path that arcs upward before dropping
            c.y += distance * dropFactor;
        }

        if (!use3D)
        {
            // In 2D mode, ensure control points have the same Z as start point
            c.z = a.z;
            d.z = a.z;
        }
    }

    /// <summary>
    /// Calculate control points for a cubic Bezier curved with path curvature (float3 version)
    /// </summary>
    public static void CalculateControlPoints(float3 a, float3 b, float curvature, float dropFactor, bool use3D, float3 offsetDirection, out float3 c, out float3 d)
    {
        var direction = math.normalize(b - a);
        var distance = math.distance(a, b);

        float3 perp;

        if (use3D)
        {
            // In 3D mode, use the specified offset direction
            perp = math.normalize(math.cross(direction, offsetDirection));

            // If the result is too small (direction and offsetDirection are nearly parallel)
            if (math.length(perp) < 0.01f)
            {
                // Try a different direction
                perp = math.normalize(math.cross(direction, direction.z > 0.5f ? new float3(0, 1, 0) : new float3(0, 0, 1)));
            }
        }
        else
        {
            // In 2D mode (XY plane), always use forward vector for consistency
            perp = math.normalize(math.cross(direction, new float3(0, 0, 1)));

            // If the direction is too vertical, use a different reference vector
            if (math.length(perp) < 0.01f)
            {
                perp = math.normalize(math.cross(direction, new float3(1, 0, 0)));
            }
        }

        // Determine if we need to invert the curve direction based on relative positions
        bool invertCurve = a.x > b.x; // If object is to the right of target, invert curve
        if (invertCurve)
        {
            perp = -perp; // Invert the perpendicular direction
        }

        // Position control points at 1/3 and 2/3 along the path
        float fractionC = 0.33f;
        float fractionD = 0.66f;

        // Calculate offset magnitude based on distance and curvature
        float offsetMagnitude = distance * 0.5f * curvature;

        // Position control points with offset
        c = a + (b - a) * fractionC + perp * offsetMagnitude;
        d = a + (b - a) * fractionD + perp * offsetMagnitude;

        // Apply vertical offset to control point C
        // This creates a curved path that dips down or rises up depending on the relative positions
        bool targetIsHigher = b.y > a.y;
        
        if (targetIsHigher)
        {
            // Target is higher - create a path that rises up with a slight dip first
            c.y -= distance * dropFactor;
        }
        else
        {
            // Target is lower - create a path that arcs upward before dropping
            c.y += distance * dropFactor;
        }

        if (!use3D)
        {
            // In 2D mode, ensure control points have the same Z as start point
            c.z = a.z;
            d.z = a.z;
        }
    }

    /// <summary>
    /// Calculate a point on a cubic Bezier curve at the specified t value (0-1)
    /// </summary>
    public static Vector3 CalculateCubicBezierPoint(float t, Vector3 p0, Vector3 p1, Vector3 p2, Vector3 p3)
    {
        float u = 1 - t;
        float tt = t * t;
        float uu = u * u;
        float uuu = uu * u;
        float ttt = tt * t;

        Vector3 point = uuu * p0;
        point += 3 * uu * t * p1;
        point += 3 * u * tt * p2;
        point += ttt * p3;

        return point;
    }

    /// <summary>
    /// Calculate a point on a cubic Bezier curve at the specified t value (0-1) (float3 version)
    /// </summary>
    public static float3 CalculateCubicBezierPoint(float t, float3 p0, float3 p1, float3 p2, float3 p3)
    {
        float u = 1 - t;
        float tt = t * t;
        float uu = u * u;
        float uuu = uu * u;
        float ttt = tt * t;

        float3 point = uuu * p0;
        point += 3 * uu * t * p1;
        point += 3 * u * tt * p2;
        point += ttt * p3;

        return point;
    }

    /// <summary>
    /// Generate a complete Bezier path between two points
    /// </summary>
    public static Vector3[] GenerateBezierPath(Vector3 start, Vector3 end, float curvature, float dropFactor, bool use3D, Vector3 offsetDirection, int resolution)
    {
        if (!use3D)
        {
            // In 2D mode, keep the same Z (XY plane)
            end.z = start.z;
        }

        CalculateControlPoints(start, end, curvature, dropFactor, use3D, offsetDirection, out Vector3 controlPointC, out Vector3 controlPointD);

        Vector3[] pathPoints = new Vector3[resolution];
        for (int i = 0; i < resolution; i++)
        {
            float t = i / (float)(resolution - 1);
            pathPoints[i] = CalculateCubicBezierPoint(t, start, controlPointC, controlPointD, end);
        }

        return pathPoints;
    }

    /// <summary>
    /// Generate a complete Bezier path between two points (float3 version)
    /// </summary>
    public static float3[] GenerateBezierPath(float3 start, float3 end, float curvature, float dropFactor, bool use3D, float3 offsetDirection, int resolution)
    {
        if (!use3D)
        {
            // In 2D mode, keep the same Z (XY plane)
            end.z = start.z;
        }

        CalculateControlPoints(start, end, curvature, dropFactor, use3D, offsetDirection, out float3 controlPointC, out float3 controlPointD);

        float3[] pathPoints = new float3[resolution];
        for (int i = 0; i < resolution; i++)
        {
            float t = i / (float)(resolution - 1);
            pathPoints[i] = CalculateCubicBezierPoint(t, start, controlPointC, controlPointD, end);
        }

        return pathPoints;
    }

    /// <summary>
    /// Get a single point along a Bezier path at the specified progress (0-1)
    /// </summary>
    public static Vector3 GetBezierPoint(Vector3 start, Vector3 end, float curvature, float dropFactor, bool use3D, Vector3 offsetDirection, float progress)
    {
        if (!use3D)
        {
            // In 2D mode, keep the same Z (XY plane)
            end.z = start.z;
        }

        CalculateControlPoints(start, end, curvature, dropFactor, use3D, offsetDirection, out Vector3 controlPointC, out Vector3 controlPointD);
        return CalculateCubicBezierPoint(progress, start, controlPointC, controlPointD, end);
    }

    /// <summary>
    /// Get a single point along a Bezier path at the specified progress (0-1) (float3 version)
    /// </summary>
    public static float3 GetBezierPoint(float3 start, float3 end, float curvature, float dropFactor, bool use3D, float3 offsetDirection, float progress)
    {
        if (!use3D)
        {
            // In 2D mode, keep the same Z (XY plane)
            end.z = start.z;
        }

        CalculateControlPoints(start, end, curvature, dropFactor, use3D, offsetDirection, out float3 controlPointC, out float3 controlPointD);
        return CalculateCubicBezierPoint(progress, start, controlPointC, controlPointD, end);
    }

    #endregion

    /// <summary>
    /// Draw a Bezier path in the scene view using Gizmos
    /// </summary>
    public static void DrawBezierPath(Vector3 start, Vector3 end, float curvature, float dropFactor, bool use3D,
        Vector3 offsetDirection, int resolution, Color pathColor, Color controlPointColor)
    {
        if (!use3D)
        {
            // In 2D mode, keep the same Z (XY plane)
            end.z = start.z;
        }

        CalculateControlPoints(start, end, curvature, dropFactor, use3D, offsetDirection, out Vector3 controlPointC, out Vector3 controlPointD);

        // Draw path
        Gizmos.color = pathColor;
        Vector3 prevPoint = start;

        for (int i = 1; i <= resolution; i++)
        {
            float t = i / (float)resolution;
            Vector3 point = CalculateCubicBezierPoint(t, start, controlPointC, controlPointD, end);
            Gizmos.DrawLine(prevPoint, point);
            prevPoint = point;
        }

        // Draw control points
        Gizmos.color = controlPointColor;
        Gizmos.DrawSphere(controlPointC, 0.1f);
        Gizmos.DrawSphere(controlPointD, 0.1f);

        // Draw start and end points
        Gizmos.color = Color.green;
        Gizmos.DrawSphere(start, 0.15f);
        Gizmos.color = Color.blue;
        Gizmos.DrawSphere(end, 0.15f);
    }

    #region Usages

    public static Tween MoveBezierPath(Transform objectToMove, Vector3 start, Vector3 end, float duration, Ease ease = Ease.InCubic)
    {
        var curvature = 0.5f;
        var dropFactor = 0.4f;
        var use3D = true;
        var offsetDirection = Vector3.zero;

        return Tween.Custom(0f, 1f, duration,
            onValueChange: progress =>
            {
                objectToMove.position = BezierUtils.GetBezierPoint(
                    start,
                    end,
                    curvature,
                    dropFactor,
                    use3D,
                    offsetDirection,
                    progress
                );
            },
            ease: ease
        );
    }

    #endregion
}