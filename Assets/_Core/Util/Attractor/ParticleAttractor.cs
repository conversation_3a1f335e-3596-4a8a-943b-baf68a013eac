using System;
using System.Collections.Generic;
using Unity.Burst;
using Unity.Collections;
using Unity.Jobs;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.Events;

namespace OnePuz.Utilities
{
    [RequireComponent(typeof(ParticleSystem))]
    public class ParticleAttractor : MonoBehaviour
    {
        public Transform target;

        [Tooltip("Delay in seconds before particles start moving")]
        public float initialDelay = 0.35f;

        [<PERSON><PERSON>("Bezier Path Settings")]
        [Tooltip("Use Bezier curve instead of straight line")]
        public bool useBezierPath = true;

        [Range(0.1f, 2.0f)]
        public float curvatureFactor = 0.45f;

        [Range(0.0f, 1.0f)]
        [Tooltip("Controls vertical path offset. Creates dip when target is higher, arc when target is lower")]
        public float pathOffsetFactor = 0.7f;

        [Tooltip("Use 3D curve instead of 2D")]
        public bool use3DPath = true;

        public Vector3 curveOffsetDirection = Vector3.forward;

        [<PERSON><PERSON>("Callbacks")]
        [Tooltip("Event triggered when a particle reaches the target")]
        public UnityEvent OnParticleReachedTarget;

        [Tooltip("Event triggered when the first particle reaches the target")]
        public UnityEvent OnFirstParticleReachedTarget;

        [Tooltip("Event triggered when the last particle reaches the target")]
        public UnityEvent OnLastParticleReachedTarget;

        [Tooltip("Threshold to consider a particle has reached the target (0-1)")]
        [Range(0.9f, 1.0f)]
        public float reachTargetThreshold = 0.95f;

        [Tooltip("Time in seconds to wait before triggering the last particle event after no new particles")]
        public float lastParticleTimeout = 0.5f;
        
        public bool IsPlaying => _particleSystem && _particleSystem.isPlaying;

        private ParticleSystem _particleSystem;
        private readonly Dictionary<uint, float> _particleBirthTimes = new Dictionary<uint, float>();
        private readonly Dictionary<uint, Vector3> _particleStartPositions = new Dictionary<uint, Vector3>();
        private readonly Dictionary<uint, float> _particleLifetimes = new Dictionary<uint, float>();
        private readonly HashSet<uint> _particlesReachedTarget = new HashSet<uint>();
        private float _elapsedTime = 0f;
        
        // Camera used for current calculations
        private Camera _currentCamera;

        // Tracking variables for first/last particle events
        private bool _firstParticleReached = false;
        private float _lastParticleReachedTime = 0f;
        private int _totalParticlesEmitted = 0;
        private bool _lastParticleEventTriggered = false;
        private bool _emissionEnded = false;

        /// <summary>
        /// Play the particle attractor with a target and camera for positioning
        /// </summary>
        /// <param name="newTarget">Target transform for particles to move towards</param>
        /// <param name="camera">Camera to use for UI calculations (will use Camera.main if null)</param>
        public void Play(Transform newTarget, int particleCount = 1, Camera camera = null)
        {
            target = newTarget;
            _currentCamera = camera;
            
            // Initialize events if null to prevent null reference exceptions
            OnParticleReachedTarget ??= new UnityEvent();
            OnFirstParticleReachedTarget ??= new UnityEvent();
            OnLastParticleReachedTarget ??= new UnityEvent();
            
            OnParticleReachedTarget.RemoveAllListeners();
            OnFirstParticleReachedTarget.RemoveAllListeners();
            OnLastParticleReachedTarget.RemoveAllListeners();

            _particleSystem ??= GetComponent<ParticleSystem>();
            if (!_particleSystem)
            {
                OLogger.LogError("ParticleSystem component not found on " + gameObject.name);
                enabled = false;
                return;
            }

            ResetParticleTracking();
            _elapsedTime = 0f;
            
            var emission = _particleSystem.emission;
            emission.enabled = true;
            emission.rateOverTime = particleCount;
            
            // Play the particle system
            _particleSystem.Play();
        }

        private void Awake()
        {
            // Initialize events if null to prevent null reference exceptions
            OnParticleReachedTarget ??= new UnityEvent();
            OnFirstParticleReachedTarget ??= new UnityEvent();
            OnLastParticleReachedTarget ??= new UnityEvent();
        }

        private void Start()
        {
            _particleSystem = GetComponent<ParticleSystem>();
            if (_particleSystem == null)
            {
                OLogger.LogError("ParticleSystem component not found on " + gameObject.name);
                enabled = false;
                return;
            }

            ResetParticleTracking();
            _elapsedTime = 0f;
        }

        private void OnEnable()
        {
            ResetParticleTracking();
        }

        // Reset tracking variables for particle events
        private void ResetParticleTracking()
        {
            _firstParticleReached = false;
            _lastParticleReachedTime = 0f;
            _totalParticlesEmitted = 0;
            _lastParticleEventTriggered = false;
            _emissionEnded = false;
            _particlesReachedTarget.Clear();
        }

        /// <summary>
        /// Gets the world position of the target, handling both UI and non-UI targets
        /// </summary>
        private Vector3 GetTargetPosition(Camera mainCamera = null)
        {
            // Use the current target or the default target
            if (!target)
                return Vector3.zero;

            // Get the camera to use
            mainCamera ??= Camera.main;
            if (!mainCamera)
                return target.position;

            // Check if target is a UI element
            var targetRectTransform = target.GetComponent<RectTransform>();
            if (targetRectTransform == null)
            {
                // Not a UI element, return world position
                return target.position;
            }

            // Target is a UI element
            // Calculate a reasonable distance from the camera
            float distanceFromCamera;
            
            if (mainCamera.orthographic)
            {
                // For orthographic camera, we can use a fixed distance
                distanceFromCamera = 10f;
            }
            else
            {
                // For perspective camera, calculate a distance based on the current distance
                // of the particle system from the camera
                var cameraToParticleSystem = transform.position - mainCamera.transform.position;
                distanceFromCamera = cameraToParticleSystem.magnitude;
                
                // Ensure the distance is reasonable (not too close, not too far)
                distanceFromCamera = Mathf.Clamp(distanceFromCamera, 1f, 20f);
            }
            
            // Use the UIPositionConverter utility to get the world position
            return UIPositionConverter.UIToWorldPosition(target, mainCamera, distanceFromCamera);
        }

        private void OnDisable()
        {
            // Clear dictionaries when disabled to prevent memory leaks
            _particleBirthTimes.Clear();
            _particleStartPositions.Clear();
            _particleLifetimes.Clear();
            _particlesReachedTarget.Clear();
        }

        private void Update()
        {
            if (!target || !_particleSystem)
                return;

            _elapsedTime += Time.deltaTime;

            // Check if the emission has ended
            if (!_emissionEnded && !_particleSystem.isEmitting && _particleSystem.particleCount > 0)
            {
                _emissionEnded = true;
                _totalParticlesEmitted = _particleSystem.particleCount;
            }

            // Check for the last particle event timeout
            if (!_lastParticleEventTriggered && _particlesReachedTarget.Count > 0 &&
                (_elapsedTime - _lastParticleReachedTime > lastParticleTimeout ||
                 (_emissionEnded && _particlesReachedTarget.Count >= _totalParticlesEmitted)))
            {
                TriggerLastParticleEvent();
            }

            // Get the max particles from the particle system
            var maxParticles = _particleSystem.main.maxParticles;
            if (maxParticles <= 0)
                return;

            var particles = new NativeArray<ParticleSystem.Particle>(maxParticles, Allocator.TempJob);
            var numParticlesAlive = _particleSystem.GetParticles(particles);

            if (numParticlesAlive <= 0)
            {
                // No particles alive, clean up and return
                particles.Dispose();

                // If we had particles that reached the target but now all particles are gone,
                // and we haven't triggered the last particle event yet, do it now
                if (!_lastParticleEventTriggered && _particlesReachedTarget.Count > 0)
                {
                    TriggerLastParticleEvent();
                }

                return;
            }

            try
            {
                var targetPosition = GetTargetPosition(_currentCamera);

                // Create arrays for birth times, start positions, and progress values
                var birthTimes = new NativeArray<float>(numParticlesAlive, Allocator.TempJob);
                var startPositions = new NativeArray<float3>(numParticlesAlive, Allocator.TempJob);
                var usesBezier = new NativeArray<int>(numParticlesAlive, Allocator.TempJob);
                var progressValues = new NativeArray<float>(numParticlesAlive, Allocator.TempJob);
                var lifetimes = new NativeArray<float>(numParticlesAlive, Allocator.TempJob);
                var particleIds = new NativeArray<uint>(numParticlesAlive, Allocator.TempJob);

                // Process each particle
                for (var i = 0; i < numParticlesAlive; i++)
                {
                    var id = particles[i].randomSeed;
                    particleIds[i] = id;

                    // Store birth time for particle if not already stored
                    if (_particleBirthTimes.TryAdd(id, _elapsedTime))
                    {
                        // Store the particle's total lifetime when we first see it
                        var startLifetime = particles[i].startLifetime;
                        _particleLifetimes.Add(id, startLifetime);
                    }

                    birthTimes[i] = _particleBirthTimes[id];
                    lifetimes[i] = _particleLifetimes[id];

                    // Store start position for particle if not already stored
                    if (!_particleStartPositions.ContainsKey(id))
                    {
                        _particleStartPositions.Add(id, particles[i].position);
                    }

                    startPositions[i] = _particleStartPositions[id];

                    // Determine if particle uses Bezier path
                    usesBezier[i] = useBezierPath ? 1 : 0;

                    // Calculate progress based on time elapsed since particle was created
                    var particleAge = _elapsedTime - birthTimes[i];
                    var totalLifetime = lifetimes[i];

                    // Only start moving after initial delay
                    if (particleAge > initialDelay)
                    {
                        // Calculate normalized progress (0 to 1) based on time after delay
                        // and adjusted for the remaining lifetime after delay
                        var activeTime = particleAge - initialDelay;
                        var activeLifetime = totalLifetime - initialDelay;

                        // Ensure we don't divide by zero
                        if (activeLifetime > 0.001f)
                        {
                            progressValues[i] = Mathf.Clamp01(activeTime / activeLifetime);
                        }
                        else
                        {
                            progressValues[i] = 1f; // If no active lifetime left, go straight to target
                        }
                    }
                    else
                    {
                        // Not yet time to move
                        progressValues[i] = 0f;
                    }
                }

                // Create and schedule the job
                var jobHandle = new ParticleUpdateJob
                {
                    particles = particles,
                    targetPosition = targetPosition,
                    birthTimes = birthTimes,
                    startPositions = startPositions,
                    usesBezier = usesBezier,
                    progressValues = progressValues,
                    curvature = curvatureFactor,
                    pathOffset = pathOffsetFactor,
                    use3D = use3DPath ? 1 : 0,
                    offsetDirection = curveOffsetDirection
                }.Schedule(numParticlesAlive, 64);

                // Wait for the job to complete
                jobHandle.Complete();

                // Update particles in particle system
                _particleSystem.SetParticles(particles, numParticlesAlive);

                // Check for particles that reached the target
                CheckParticlesReachedTarget(particleIds, progressValues, numParticlesAlive);

                // Remove particles that no longer exist
                CleanupParticleData(particles, numParticlesAlive);

                // Free memory
                birthTimes.Dispose();
                startPositions.Dispose();
                usesBezier.Dispose();
                progressValues.Dispose();
                lifetimes.Dispose();
                particleIds.Dispose();
            }
            catch (Exception e)
            {
                OLogger.LogError($"Error in particleAttractorLinear: {e.Message}\n{e.StackTrace}");
            }
            finally
            {
                // Always dispose of the native array to prevent memory leaks
                if (particles.IsCreated)
                    particles.Dispose();
            }
        }

        private void CheckParticlesReachedTarget(NativeArray<uint> particleIds, NativeArray<float> progressValues, int count)
        {
            if (OnParticleReachedTarget == null)
                return;

            for (var i = 0; i < count; i++)
            {
                var id = particleIds[i];
                var progress = progressValues[i];

                // Check if this particle has reached the target and hasn't been reported yet
                if (!(progress >= reachTargetThreshold) || !_particlesReachedTarget.Add(id)) continue;

                // Add to the set of particles that reached the target
                _lastParticleReachedTime = _elapsedTime;

                // Invoke the individual particle callback
                OnParticleReachedTarget.Invoke();

                // Check if this is the first particle to reach the target
                if (_firstParticleReached) continue;

                _firstParticleReached = true;
                OnFirstParticleReachedTarget.Invoke();
            }
        }

        private void TriggerLastParticleEvent()
        {
            if (!_lastParticleEventTriggered && _particlesReachedTarget.Count > 0)
            {
                _lastParticleEventTriggered = true;
                OnLastParticleReachedTarget.Invoke();

                _currentCamera = null;
                target = null;
            }
        }

        private void CleanupParticleData(NativeArray<ParticleSystem.Particle> particles, int count)
        {
            var keysToRemove = new List<uint>();

            // Find all keys that no longer exist in current particles
            foreach (var key in _particleBirthTimes.Keys)
            {
                var found = false;
                for (var i = 0; i < count; i++)
                {
                    if (particles[i].randomSeed != key) continue;
                    found = true;
                    break;
                }

                if (!found)
                    keysToRemove.Add(key);
            }

            // Remove keys that no longer exist
            foreach (var key in keysToRemove)
            {
                _particleBirthTimes.Remove(key);
                _particleStartPositions.Remove(key);
                _particleLifetimes.Remove(key);
                _particlesReachedTarget.Remove(key);
            }
        }

        [BurstCompile]
        private struct ParticleUpdateJob : IJobParallelFor
        {
            [NativeDisableParallelForRestriction] public NativeArray<ParticleSystem.Particle> particles;
            [ReadOnly] public NativeArray<float> birthTimes;
            [ReadOnly] public NativeArray<float3> startPositions;
            [ReadOnly] public NativeArray<int> usesBezier;
            [ReadOnly] public NativeArray<float> progressValues;
            public float3 targetPosition;
            public float curvature;
            public float pathOffset;
            public int use3D;
            public float3 offsetDirection;

            public void Execute(int index)
            {
                var particle = particles[index];
                var progress = progressValues[index];

                // Only update position if we have a non-zero progress value
                if (progress > 0)
                {
                    if (usesBezier[index] == 1)
                    {
                        // Use Bezier curve
                        float3 startPos = startPositions[index];
                        particle.position = GetBezierPoint(startPos, targetPosition, progress);
                    }
                    else
                    {
                        // Use linear interpolation (straight line)
                        particle.position = math.lerp(startPositions[index], targetPosition, progress);
                    }

                    particles[index] = particle;
                }
            }

            // Bezier methods implemented directly in the job
            private float3 GetBezierPoint(float3 start, float3 end, float progress)
            {
                if (use3D == 0)
                {
                    // In 2D mode, keep the same Z (XY plane)
                    end.z = start.z;
                }

                CalculateControlPoints(start, end, out float3 controlPointC, out float3 controlPointD);
                return CalculateCubicBezierPoint(progress, start, controlPointC, controlPointD, end);
            }

            private void CalculateControlPoints(float3 a, float3 b, out float3 c, out float3 d)
            {
                var direction = math.normalize(b - a);
                var distance = math.distance(a, b);

                float3 perp;

                if (use3D == 1)
                {
                    // In 3D mode, use the specified offset direction
                    perp = math.normalize(math.cross(direction, offsetDirection));

                    // If the result is too small (direction and offsetDirection are nearly parallel)
                    if (math.length(perp) < 0.01f)
                    {
                        // Try a different direction
                        perp = math.normalize(math.cross(direction, direction.z > 0.5f ? new float3(0, 1, 0) : new float3(0, 0, 1)));
                    }
                }
                else
                {
                    // In 2D mode (XY plane), always use forward vector for consistency
                    perp = math.normalize(math.cross(direction, new float3(0, 0, 1)));

                    // If the direction is too vertical, use a different reference vector
                    if (math.length(perp) < 0.01f)
                    {
                        perp = math.normalize(math.cross(direction, new float3(1, 0, 0)));
                    }
                }

                // Determine if we need to invert the curve direction based on relative positions
                bool invertCurve = a.x > b.x; // If object is to the right of target, invert curve
                if (invertCurve)
                {
                    perp = -perp; // Invert the perpendicular direction
                }

                // Position control points at 1/3 and 2/3 along the path
                float fractionC = 0.33f;
                float fractionD = 0.66f;

                // Calculate offset magnitude based on distance and curvature
                float offsetMagnitude = distance * 0.5f * curvature;

                // Position control points with offset
                c = a + (b - a) * fractionC + perp * offsetMagnitude;
                d = a + (b - a) * fractionD + perp * offsetMagnitude;

                // Apply vertical offset to control point C
                // This creates a curved path that dips down or rises up depending on the relative positions
                bool targetIsHigher = b.y > a.y;
                
                if (targetIsHigher)
                {
                    // Target is higher - create a path that rises up with a slight dip first
                    c.y -= distance * pathOffset;
                }
                else
                {
                    // Target is lower - create a path that arcs upward before dropping
                    c.y += distance * pathOffset;
                }

                if (use3D == 0)
                {
                    // In 2D mode, ensure control points have the same Z as start point
                    c.z = a.z;
                    d.z = a.z;
                }
            }

            private float3 CalculateCubicBezierPoint(float t, float3 p0, float3 p1, float3 p2, float3 p3)
            {
                var u = 1 - t;
                var tt = t * t;
                var uu = u * u;
                var uuu = uu * u;
                var ttt = tt * t;

                var point = uuu * p0;
                point += 3 * uu * t * p1;
                point += 3 * u * tt * p2;
                point += ttt * p3;

                return point;
            }
        }

        // Draw Bezier curved in the Scene view
        private void OnDrawGizmosSelected()
        {
            if (useBezierPath && target != null)
            {
                // Draw curve from the current position to target
                var startPos = transform.position;
                var endPos = target.position;

                BezierUtils.DrawBezierPath(
                    startPos,
                    endPos,
                    curvatureFactor,
                    pathOffsetFactor,
                    use3DPath,
                    curveOffsetDirection,
                    20, // resolution
                    Color.cyan,
                    Color.red
                );
            }
        }
    }
}