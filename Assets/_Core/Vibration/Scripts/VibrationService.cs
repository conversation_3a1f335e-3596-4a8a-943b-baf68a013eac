using System;
using OnePuz.Data;
using OnePuz.Vibration_Master;
using OnePuz.Services;
using UnityEngine;

namespace OnePuz.Services
{
    public interface IVibrationService
    {
        void EnableVibration(bool value);
        void Vibrate(VibrationType vibrationType = VibrationType.LIGHT);
    }

    public enum VibrationType
    {
        LIGHT,
        MEDIUM,
        HEAVY
    }

    public class VibrationService : IVibrationService, IService
    {
        public void EnableVibration(bool value)
        {
            DataShortcut.Setting.VibrationEnabled = value;
        }

        public void Vibrate(VibrationType vibrationType = VibrationType.LIGHT)
        {
            if (Application.isEditor || !DataShortcut.Setting.VibrationEnabled) return;
            switch (vibrationType)
            {
                case VibrationType.LIGHT:
                    Vibration.VibratePop();
                    break;
                case VibrationType.MEDIUM:
                    Vibration.VibratePeek();
                    break;
                case VibrationType.HEAVY:
                    Vibration.Vibrate();
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(vibrationType), vibrationType, null);
            }
        }
    }
}