using System;
using System.Collections.Generic;
using PrimeTween;
using UnityEngine;

namespace OnePuz.Extensions
{
    public static class AnimatorExtension
    {
        public static void SetState(this Animator animator, int stateNameHash, int layer = 0, bool isReversed = false)
        {
            if (!isReversed)
            {
                animator.Play(stateNameHash, layer, 0);
                animator.Update(Time.deltaTime);
            }
            else
            {
                animator.Play(stateNameHash, layer, 1);
                animator.Update(-Time.deltaTime);
            }
        }

        public static bool IsAlreadyInState(this Animator animator, int stateNameHash, int layer = 0, bool isReversed = false)
        {
            var currentStateInfo = animator.GetCurrentAnimatorStateInfo(layer);
            if (currentStateInfo.shortNameHash != stateNameHash)
                return false;
            switch (currentStateInfo.normalizedTime)
            {
                case >= 1f when !isReversed:
                case <= 0f when isReversed:
                    return true;
                default:
                    return false;
            }
        }

        private static float GetClipLength(this Animator animator, int stateNameHash)
        {
            if (animator.runtimeAnimatorController is AnimatorOverrideController @override)
            {
                var listClipPairs = new List<KeyValuePair<AnimationClip, AnimationClip>>();
                @override.GetOverrides(listClipPairs);
                return listClipPairs.Find(pair => Animator.StringToHash(pair.Key.name) == stateNameHash)!.Value.length;
            }

            return Array.Find(animator.runtimeAnimatorController.animationClips, 
                clip => Animator.StringToHash(clip.name) == stateNameHash)!.length;
        }

        public static Sequence PlayNested(this Animator animator, int stateNameHash, float duration = -1, bool isFirstUpdate = false, float delay = 0, int cycles = 1, int layer = 0)
        {
            if (!animator.gameObject.activeSelf) animator.gameObject.SetActive(true);
            if (isFirstUpdate) animator.SetState(stateNameHash, layer);
            var length = animator.GetClipLength(stateNameHash);
            if (duration <= 0) duration = length;
            
            var sequencer = Sequence.Create(cycles).InsertCallback(0, animator, @this => @this.SetState(stateNameHash, layer))
                .Group(Tween.Custom(animator, 0, 1, duration, (@this, _)
                    => @this.Update(Time.deltaTime * length / duration), startDelay: delay));
            if (cycles is < 0 or > 1)
                sequencer.OnComplete(animator, @this => @this.SetState(stateNameHash, layer));
            return sequencer;
        }

        public static Sequence PlayNestedReverse(this Animator animator, int stateNameHash, float duration = -1, bool isFirstUpdate = false, float delay = 0, int cycles = 1, int layer = 0)
        {
            if (!animator.gameObject.activeSelf) animator.gameObject.SetActive(true);
            if (isFirstUpdate) animator.SetState(stateNameHash, layer, true);
            var length = animator.GetClipLength(stateNameHash);
            if (duration <= 0) duration = length;

            var sequencer = Sequence.Create(cycles).InsertCallback(0, animator, @this => @this.SetState(stateNameHash, layer, true))
                .Group(Tween.Custom(animator, 0, 1, duration, (@this, _) 
                    => @this.Update(-Time.deltaTime * length / duration), startDelay: delay));
            if (cycles is < 0 or > 1)
                sequencer.OnComplete(animator, @this => @this.SetState(stateNameHash, layer, true));
            return sequencer;
        }

        public static Tween PlayManual(this Animator animator, int stateNameHash, float duration = -1, float delay = 0, int cycles = 1, int layer = 0)
        {
            Tween.StopAll(animator);
            if (!animator.gameObject.activeSelf) animator.gameObject.SetActive(true);
            animator.SetState(stateNameHash, layer);
            var length = animator.GetCurrentAnimatorStateInfo(layer).length;
            if (duration <= 0) duration = length;
            var tween = Tween.Custom(animator, 0, 1, duration, (@this, _) => @this.Update(Time.deltaTime * length / duration), cycles: cycles, startDelay: delay);
            if(cycles is < 0 or > 1)
                tween.OnComplete(animator, @this => @this.SetState(stateNameHash, layer));
            return tween;
        }

        public static Tween PlayReverse(this Animator animator, int stateNameHash, float duration = -1, float delay = 0, int cycles = 1, int layer = 0)
        {
            Tween.StopAll(animator);
            if (!animator.gameObject.activeSelf) animator.gameObject.SetActive(true);
            animator.SetState(stateNameHash, layer, true);
            var length = animator.GetCurrentAnimatorStateInfo(0).length;
            if (duration <= 0) duration = length;
            var tween = Tween.Custom(animator, 0, 1, duration, (@this, _) => @this.Update(-Time.deltaTime * length / duration), cycles: cycles, startDelay: delay);
            if(cycles is < 0 or > 1)
                tween.OnComplete(animator, @this => @this.SetState(stateNameHash, layer, true));
            return tween;
        }

        public static Tween BezierMove(this Transform target, float duration, params Vector3[] path)
        {
            return path.Length switch
            {
                3 => Tween.Custom(target, 0, 1, duration, (@this, t) => @this.position = GetQuadraticBezierPoint(t, path[0], path[1], path[2])),
                4 => Tween.Custom(target, 0, 1, duration, (@this, t) => @this.position = GetCubicBezierPoint(t, path[0], path[1], path[2], path[3])),
                _ => throw new ArgumentException("Path length must is 3 or 4.")
            };
        }
        
        public static Tween BezierMove(this RectTransform target, float duration, params Vector2[] path)
        {
            return path.Length switch
            {
                3 => Tween.Custom(target, 0, 1, duration, (@this, t) => @this.anchoredPosition = GetQuadraticBezierPoint(t, path[0], path[1], path[2])),
                4 => Tween.Custom(target, 0, 1, duration, (@this, t) => @this.anchoredPosition = GetCubicBezierPoint(t, path[0], path[1], path[2], path[3])),
                _ => throw new ArgumentException("Path length must is 3 or 4.")
            };
        }
        
        
        private static Vector3 GetQuadraticBezierPoint(float t, Vector3 p0, Vector3 p1, Vector3 p2) 
            => (1 - t) * (1 - t) * p0 + 2 * (1 - t) * t * p1 + t * t * p2;

        private static Vector3 GetCubicBezierPoint(float t, Vector3 p0, Vector3 p1, Vector3 p2, Vector3 p3) 
            => (1 - t) * (1 - t) * (1 - t) * p0 + 3 * (1 - t) * (1 - t) * t * p1 + 3 * (1 - t) * t * t * p2 + t * t * t * p3;
        
        private static Vector2 GetQuadraticBezierPoint(float t, Vector2 p0, Vector2 p1, Vector2 p2) 
            => (1 - t) * (1 - t) * p0 + 2 * (1 - t) * t * p1 + t * t * p2;
        
        private static Vector2 GetCubicBezierPoint(float t, Vector2 p0, Vector2 p1, Vector2 p2, Vector2 p3) 
            => (1 - t) * (1 - t) * (1 - t) * p0 + 3 * (1 - t) * (1 - t) * t * p1 + 3 * (1 - t) * t * t * p2 + t * t * t * p3;
    }
}