using System;
using UnityEditor;
using UnityEngine;

namespace OnePuz.Attributes.Editor
{
    [CustomPropertyDrawer(typeof(DropDownValueOfAttribute))]
    public class DropDownValueOfDrawer : PropertyDrawer
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            var _attribute = (DropDownValueOfAttribute)attribute;
            int index = Array.IndexOf(_attribute.fieldValues, property.stringValue);
            if (index < 0)
            {
                EditorGUI.BeginChangeCheck();
                index = EditorGUI.Popup(position, label.text, _attribute.fieldNameFlags.Length - 1, _attribute.fieldNameFlags, PresetGUIStyle.errorPopup);
                if (EditorGUI.EndChangeCheck())
                {
                    property.stringValue = _attribute.fieldValues[index];
                    return;
                }
                EditorGUILayout.HelpBox("KeyNotFoundException: Deselect to fix it.", MessageType.Error);
                return;
            }
            
            EditorGUI.BeginChangeCheck();
            index = EditorGUI.Popup( position, label.text, index, _attribute.fieldNames);
            if (EditorGUI.EndChangeCheck())
                property.stringValue = _attribute.fieldValues[index];
        }
    }
}