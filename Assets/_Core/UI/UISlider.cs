using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.UI;
using Tween = PrimeTween.Tween;

namespace OnePuz.UI
{
    [RequireComponent(typeof(Slider))]
    public class UISlider : MonoBehaviour
    {
        public SimpleFloatCallback OnValueChanged { set; get; }
        public float Value => _slider.value;

        private Slider _slider;
        private TMPro.TextMeshProUGUI _valueText;
        private Tween _tween;

        private void Awake()
        {
            Init();
        }

        public void Init()
        {
            _slider = GetComponent<Slider>();
            _slider.value = 0;
            _slider.maxValue = 100;
            _slider.interactable = false;

            _valueText = transform.Find("Value").GetComponent<TMPro.TextMeshProUGUI>();
        }

        public void Proceed(float progressPercentage, float duration)
        {
            Tween.Custom(_slider.value, progressPercentage, duration: duration, onValueChange: newValue =>
            {
                _slider.value = newValue;
                _valueText.text = $"{newValue:N0}%";

                OnValueChanged?.Invoke(_slider.value);
            });
        }

        public async UniTask ProcessAsync(float progressPercentage, float duration, CancellationToken token)
        {
            _tween.Stop();
            _tween = Tween.Custom(_slider.value, progressPercentage, duration: duration, onValueChange: newValue =>
            {
                _slider.value = newValue;
                _valueText.text = $"{newValue:N0}%";

                OnValueChanged?.Invoke(newValue);
            });
            await _tween.ToUniTask(cancellationToken: token);
        }
    }
}