Pass
{
    Tags { "LightMode"="SceneSelectionPass" }

    ZWrite On
    Blend Off
    
    HLSLPROGRAM
    #define VFX_PASSDEPTH VFX_PASSDEPTH_SELECTION
    ${VFXIncludeRP("Templates/ParticlePlanarPrimitivesLit/PassDepthOrMV.template")}
    ENDHLSL
}

Pass
{
    Tags { "LightMode"="Picking" }

    ZWrite On
    Blend Off
    
    HLSLPROGRAM
    #define VFX_PASSDEPTH VFX_PASSDEPTH_PICKING
    ${VFXIncludeRP("Templates/ParticlePlanarPrimitivesLit/PassDepthOrMV.template")}
    ENDHLSL
}
